import service from '@/common/utils/request';

/**
 * 实时数据路由下的详细数据列表
 * @param {*} params
 * @returns
 */
export const getRealDataList = (params) => {
  return service.post('/api/dailyvisit/page/userbehavior', params);
};

/**
 * 实时数据路由下今日数据统计
 * @param {*} params
 * @returns
 */
export const getRealTodayDataInfo = (params) => {
  return service.get('/api/userbehavior/today/statistics', params);
};

/**
 *  实时数据路由下的导出实时数据
 * @param {*} params
 * @returns
 */
export const downloadRealData = (params) => {
  return service.post('/api/dailyvisit/export/userbehavior/excel', params, {
    responseType: 'blob',
  });
};

/**
 *当日累计会话次数
 * @param {*} params
 * @returns
 */
export const getTodaySessionNum = (params) => {
  return service.get('/api/usersession/todaysessionnum', params);
};

/**
 *用户会话统计-详细数据
 * @param {*} params
 * @returns
 */
export const pageUserSession = (params) => {
  return service.post('/api/usersessionstatistics/page/usersession', params);
};

/**
 *用户会话统计-导出详细数据
 * @param {*} params
 * @returns
 */
export const exportUserSessionData = (params) => {
  return service.post('/api/usersessionstatistics/export/excel', params, {
    responseType: 'blob',
  });
};

/**
 *功能使用统计-详细数据
 * @param {*} params
 * @returns
 */
export const getDocumentUsage = (params) => {
  return service.post('/api/userStatistics/getDocumentUsage', params);
};

/**
 *功能使用统计-导出详细数据
 * @param {*} params
 * @returns
 */
export const exportDocumentUsageExcel = (params) => {
  return service.post('/api/userStatistics/exportDocumentUsageExcel', params, {
    responseType: 'blob',
  });
};

/**
 * 获取系统用户列表
 * @param {*} params
 * @returns
 */
export const getUserList = (params) => {
  return service.post('/api/userManage/getSystemUserInfo', params);
};

/**
 *获取访问来源列表
 * @param {*} params
 * @returns
 */
export const getSessionTypes = (params) => {
  return service.get('/api/userStatistics/getSessionTypes', params);
};

/**
 * 获取会话记录列表
 * @param {*} params
 * @returns
 */
export const getChatRecordList = (params) => {
  return service.post('api/usersession/page/details', params);
};

/**
 *积分管理-今日积分发放数量
 * @param {*} params
 * @returns
 */
export const getPointsdetailsTodaysum = (params) => {
  return service.get('/api/pointsdetails/todaysum', params);
};

/**
 * 积分管理-详细数据-用户姓名下拉框（工号和姓名都支持模糊查询）
 * @param {string} userName 用户名查询关键词
 * @returns {Promise} API响应
 */
export const listTop20UserName = (userName = '') => {
  return service.get(`/api/sysuser/list/top20?userName=${encodeURIComponent(userName)}`);
};

/**
 * 积分管理-详细数据列表
 * @param {*} params
 * @returns
 */
export const getPointsdetailsList = (params) => {
  return service.post('/api/pointsdetails/page', params);
};

/**
 * 积分管理-详细数据列表导出
 * @param {*} params
 * @returns
 */
export const pointsdetailsExportExcel = (params) => {
  return service.post('/api/pointsdetails/export/excel', params, {
    responseType: 'blob',
  });
};
/**
 * 积分管理-部门积分
 * @param {*} params
 * @returns
 */
export const orgExportExcel = (params) => {
  return service.post('/api/pointsdetails/export/org/excel', params, {
    responseType: 'blob',
  });
};
/**
 * 积分管理-个人积分
 * @param {*} params
 * @returns
 */
export const personExportExcel = (params) => {
  return service.post('/api/pointsdetails/export/person/excel', params, {
    responseType: 'blob',
  });
};

// 积分管理-日活用户数量表
export const getUserChartsStats = (params) => {
  return service.post('/api/dailyvisit/getUserChartsStats', params);
};
// 积分管理-日访问数量表
export const getVisitChartsStats = (params) => {
  return service.post('/api/uservisit/getVisitChartsStats', params);
};

/**
 *反馈管理-今日积分发放数量
 * @param {*} params
 * @returns
 */
export const getFeedbackCounts = (params) => {
  return service.get('/api/userfeedback/getFeedbackCounts', params);
};
/**
 * 反馈管理-详细数据列表
 * @param {*} params
 * @returns
 */
export const getUserfeedbackList = (params) => {
  return service.post('/api/userfeedback/page', params);
};
/**
 * 反馈管理-详细数据列表导出
 * @param {*} params
 * @returns
 */
export const userfeedbackExportExcel = (params) => {
  return service.post('/api/userfeedback/export', params, {
    responseType: 'blob',
  });
};

/**
 * 消息推送管理 - 获取用户树
 */
export const getUserTreeRequest = (params) => {
  console.log(params);
  return service.get('/api/elinkUser/tree/full', { params });
};

/**
 * 消息推送管理 - 获取用户树，在不选时间的条件下
 */
export function getUserTreeRequest1(departmentId) {
  return service.get(`/api/elinkUser/list/${departmentId}`);
}

/**
 * 消息推送管理 - 新增推送
 */
export const addPushRequest = (data) => {
  return service.post('/api/push/add', data);
};

/**
 * 消息推送管理 - 获取列表
 *
 */
export const getPushListRequest = (data) => {
  return service.post('/api/push/page', data);
};

/**
 * 消息推送管理-推送按钮
 */
export const doPushRequest = (data) => {
  return service.post('/api/push/doPush', data);
};

/**
 * 消息推送管理 - 获取卡片下拉
 */
export const getCardListRequest = () => {
  return service.get('/api/push/message/type');
};

/**
 * 消息推送管理-推送消息用户日志列表
 */
export const getPushLogListRequest = (id, data) => {
  return service.post(`/api/pushuser/list/${id}`, data);
};

/**
 * 消息推送管理  -  消息列表导出
 *
 */
export const exportPushListRequest = (data) => {
  return service.post('/api/push/export/excel', data, {
    responseType: 'blob',
  });
};

/**
 * 消息推送管理-推送消息用户日志列表导出
 */
export const exportPushLogListRequest = (id, data) => {
  return service.post(`/api/pushuser/export/excel/${id}`, data, {
    responseType: 'blob',
  });
};

/**
 * 消息推送管理 - 同步用户功能
 */
export const syncUserRequest = () => {
  return service.get('/api/getAllDepartmentUserInfo');
};

/**
 * 消息推送管理 - 删除推送
 */
export const deletePushRequest = (data) => {
  return service.post('/api/push/delete', data);
};

/**
 * 消息推送管理  - 触发部门与用户数据同步任务
 */
export const triggerSyncRequest = () => {
  return service.get('/api/startSyncElink');
};
/**
 * 消息推送管理 - 查询同步任务状态
 */
export const getSyncStatusRequest = () => {
  return service.get('/api/getSyncElinkStatus?taskId=department_user_sync');
};

/** \
 * 消息推送管理 - IDS获取树
 */
export const getIdsTreeRequest = (data) => {
  return service.post('/api/elinkUser/get/ids', data);
};

/**
 * 反馈管理 新增回复
 */
export const addReplyRequest = (id, data) => {
  return service.post(`/api/userfeedbackresponse/add/${id}`, data);
};

/**
 * 反馈管理 反馈列表查询
 */
export const getFeedbackListRequest = (id, params) => {
  return service.post(`/api/userfeedbackresponse/page/${id}`, params);
};

/**
 * 积分规则新增或修改
 */
export const pointstaskAddEdit = (params) => {
  return service.post('/api/pointstask/add', params);
};
/**
 * 任务积分获取全部列表
 */
export const pointstaskList = (params) => {
  return service.post('/api/pointstask/list', params);
};

/**
 * 联网管理 - 系统联网次数
 */
export const searchfuncInfo = () => {
  return service.get('/api/searchfunc/info');
};
/**
 * 联网管理 - 联网搜索操作次数
 */
export const searchfuncNum = () => {
  return service.get('/api/searchfunc/num');
};
/**
 * 联网管理 - 联网搜索操作次数
 */
export const searchfuncStatus = () => {
  return service.get('/api/searchfunc/status');
};
/**
 * 联网搜索分页查询
 */
export const searchfuncdetailsList = (params) => {
  return service.post('/api/searchfuncdetails/page', params);
};
/**
 * 联网搜索导出excel
 */
export const searchfuncdetailsExport = (params) => {
  return service.post('/api/searchfuncdetails/export', params, {
    responseType: 'blob',
  });
};
/**
 * 版本管理-新增版本
 */
export function addVersion(params) {
  return service.post('/api/qaversion/add', params);
}
/**
 * 版本管理-版本删除
 * @param {array} params string
 * @returns
 */
export function deleteVersion(params) {
  return service.post('/api/qaversion/delete', params);
}
/**
 * 版本管理-全部列表
 */
export function VersionList(params) {
  return service.post('/api/qaversion/list', params);
}
/**
 * 版本管理-版本管理分页查询
 */
export function VersionPage(params) {
  return service.post('/api/qaversion/page', params);
}
/**
 * 版本管理-版本更新
 */
export function updateVersion(params) {
  return service.post('/api/qaversion/update', params);
}

/**
 * 灰度发布管理 - 左侧部门树
 */
export const getDepartmentTree = () => {
  return service.get('/api/elinkDepartment/tree/full');
};

/**
 * 灰度发布管理-部门下用户分页及最新灰度版本信息查询
 */
export const getQaversionusage = (departmentId, data) => {
  return service.post(`/api/qaversionusage/page/${departmentId}`, data);
};

/**
 * 灰度发布管理-每个用户的灰度发布历史记录
 */
export const getQaversionusageList = (userId, data) => {
  return service.get(`/api/qaversionusage/histories/${userId}`, data);
};

/**
 * 灰度发布管理-新增授权用户可使用版本信息
 */
export const addQaversionusage = (data) => {
  return service.post('/api/qaversionusage/add', data);
};

/**
 * 知识库管理-顶级组织信息
 */
export const getOrgList = (data) => {
  return service.post('/api/knowledge/permission/searchOrgList', data);
};
/**
 * 知识库管理-次级组织信息
 */
export const subOrgList = (data) => {
  return service.post('/api/knowledge/permission/subOrgList', data);
};
/**
 * 知识库管理-详细用户信息
 */
export const getOrgUserList = (data) => {
  return service.post('/api/knowledge/user/pageOrgUser', data);
};
/**
 * 知识库管理-知识库列表
 */
export const getKnowledgeList = (data) => {
  return service.post('/api/knowledge/list', data);
};
/**
 * 知识库权限新增
 *
 */
export function addKnowledge(data) {
  return service.post('/api/knowledge/add', data);
}
/**
 * 知识库启用-停用
 *
 */
export function enableAndDisable(id, status) {
  return service.post(`/api/knowledge/enable/${id}/${status}`);
}
/**
 * 获取知识库权限配置
 *
 */
export function getKnowledgePermission(id) {
  return service.get(`/api/knowledge/permission/${id}`);
}
/**
 * 知识库权限配置，修改用户配置提交
 *
 */
export function grantPermission(id, params) {
  return service.post(`/api/knowledge/grant/permission/${id}`, params);
}
/**
 * 敏感词词库-词库列表
 */
export const getSensitiveWordList = (data) => {
  return service.post('/api/sensitive/word/list', data);
};
/**
 * 敏感词词库-词库分页列表
 */
export const getSensitiveWordPage = (data) => {
  return service.post('/api/sensitive/word/page', data);
};
/**
 * 敏感词词库-词库新增
 */
export const saveOrUpdateSensitiveWord = (data) => {
  return service.post('/api/sensitive/word/saveOrUpdate', data);
};
/**
 * 敏感词词库-同条敏感词类型新增敏感词
 */
export const batchAddSameType = (data) => {
  return service.post('/api/sensitive/word/batchAddSameType', data);
};
/**
 * 敏感词词库-敏感词详细列表
 */
export const typeStatPage = (data) => {
  return service.post('/api/sensitive/word/typeStatPage', data);
};

/**
 * 大屏维护获取数据
 */
export const getScreenData = (data) => {
  return service.post('/api/dashboarddata/list', data);
};

/*大屏维护 更新值*/
export const updateScreenData = (data) => {
  return service.post('/api/dashboarddata/update', data);
};
