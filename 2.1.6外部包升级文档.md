# sn-app-system | sn-base-layout hotfix版本升级文档

## 安装

```js
npm i sn-app-system@2.1.6-hotfix.1 sn-base-layout@2.1.6-hotfix.1
```

## 项目调整

### vue.config.js

`transpileDependencies`：

```diff
module.exports = defineConfig({
+   transpileDependencies: ["sn-base-layout"], // 将 sn-base-layout 添加到编译中
})
```

`alias`:

```diff
module.exports = defineConfig({
   configureWebpack: {
       resolve: {
           alias: {
               // ...
+             "@packSystem": "sn-app-system/packSystem",
+        	  "@packLayout": "sn-base-layout/packLayout",
           }
       }
   }
})
```

`chainWebpack`:

```diff
chainWebpack(config) {
    config.module
      .rule("js")
      .test(/\.js$/)
      .include.add(resolve("src"))
      .add(resolve("src"))
      .end()
+     .include.add(resolve("node_modules/sn-base-layout"))
+     .end()
      .use("babel")
      .loader("babel-loader")
      .options({
        presets: [
          [
            "@babel/preset-env",
            {
              modules: false,
            },
          ],
        ],
      });
}
```

`splitChunks`：

```diff
    config.optimization.splitChunks({
          chunks: "all",
          cacheGroups: {
			// ...
            snpitUI: {
              // 将elementUI拆分为单个包
              name: "chunk-snpitUI",
              // 重量需要大于libs和app，否则将打包到libs或app中
              priority: 20,
              // 为了适应cnpm
              test: /[\\/]node_modules[\\/]_?snpit-ui(.*)/,
            },
+            system: {
+              name: "chunk-snAppSystem",
+              priority: 20,
+              test: /[\\/]node_modules[\\/]_?sn-app-system(.*)/,
+            },
+            layout: {
+              name: "chunk-snBaseLayout",
+              priority: 20,
+              test: /[\\/]node_modules[\\/]_?sn-base-layout(.*)/,
+            },
+            pluginModules: {
+              name: "chunk-pluginModules",
+              priority: 20,
+              test: /[\\/]node_modules[\\/]_?sn-plugin-modules(.*)/,
+            },
            snBpmV2: {
              // 将elementUI拆分为单个包
              name: "chunk-snBpmV2",
              // 重量需要大于libs和app，否则将打包到libs或app中
              priority: 20,
              // 为了适应cnpm
              test: /[\\/]node_modules[\\/]_?sn-bpm-v2(.*)/,
            },
            // ...
          },
        });
```

