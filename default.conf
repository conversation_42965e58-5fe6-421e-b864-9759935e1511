server {
    listen 80;
    listen [::]:80;

    location ^~ /api/ {
        proxy_pass              http://gateway.dev.snpit.com:8080/;
        proxy_set_header        Host localhost;
        proxy_set_header        X-Real-IP $remote_addr;
        proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
    }
    location /api/bpm {
                        rewrite  ^/api/bpm/(.*)$ /$1 break;
                        proxy_set_header Host $http_host;
                        proxy_set_header X-Real-IP $remote_addr;
                        proxy_set_header REMOTE-HOST $remote_addr;
                        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                        proxy_pass http://ui.bpm.dev.snpit.com:9999/;
        }


    location ^~ /webMessage/ {
        proxy_pass              http://ui.message.dev.snpit.com:9106/;
        proxy_http_version      1.1;
        proxy_read_timeout      3600s;
        proxy_set_header        Upgrade $http_upgrade;
        proxy_set_header        Connection "upgrade";
    }

    location ^~ /miniofile/ {
        proxy_pass              http://minio.dev.snpit.com:9000/;
        proxy_set_header        Host localhost;
        proxy_set_header        X-Real-IP $remote_addr;
        proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
    }

    location / {
        add_header 'Access-Control-Allow-Origin' '*';
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
        root   /opt/isimple/web/;
        index  index.html index.htm;
        if (!-e $request_filename) {
            rewrite ^(.*)$ /index.html?s=$1 last;
            break;
        }
    }

}