<!DOCTYPE html>
<html lang="">

<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width,initial-scale=1.0">
  <link rel="icon" href="<%= BASE_URL %>logo.png">
  <!-- <script src="https://cdn.bootcdn.net/ajax/libs/jsencrypt/3.0.0-rc.1/jsencrypt.min.js"></script> -->
  <script>
    // window.BASE_URL="http://backend.example.com"
  </script>
  <title>
    <%= webpackConfig.name %>
  </title>
  <style>
    html {
      height: 100%;
    }
#app-container{
  height:100%;
}
    body {
      margin: 0;
      height: 100%;
    }
    #loader-wrapper {
      width: 100%;
      height: 100%;
      background-image: radial-gradient(circle farthest-corner at center, #6d6eef 0%, #3C3DFA 100%);
    }

    .loader {
      position: absolute;
      top: calc(50% - 32px);
      left: calc(50% - 32px);
      width: 64px;
      height: 64px;
      border-radius: 50%;
      perspective: 800px;
    }

    #text {
      position: absolute;
      width: 100%;
      top: calc(50% + 62px);
      text-align: center;
      color: #fff;
    }

    .inner {
      position: absolute;
      box-sizing: border-box;
      width: 100%;
      height: 100%;
      border-radius: 50%;
    }

    .inner.one {
      left: 0%;
      top: 0%;
      animation: rotate-one 1s linear infinite;
      border-bottom: 3px solid #EFEFFA;
    }

    .inner.two {
      right: 0%;
      top: 0%;
      animation: rotate-two 1s linear infinite;
      border-right: 3px solid #EFEFFA;
    }

    .inner.three {
      right: 0%;
      bottom: 0%;
      animation: rotate-three 1s linear infinite;
      border-top: 3px solid #EFEFFA;
    }

    @keyframes rotate-one {
      0% {
        transform: rotateX(35deg) rotateY(-45deg) rotateZ(0deg);
      }

      100% {
        transform: rotateX(35deg) rotateY(-45deg) rotateZ(360deg);
      }
    }

    @keyframes rotate-two {
      0% {
        transform: rotateX(50deg) rotateY(10deg) rotateZ(0deg);
      }

      100% {
        transform: rotateX(50deg) rotateY(10deg) rotateZ(360deg);
      }
    }

    @keyframes rotate-three {
      0% {
        transform: rotateX(35deg) rotateY(55deg) rotateZ(0deg);
      }

      100% {
        transform: rotateX(35deg) rotateY(55deg) rotateZ(360deg);
      }
    }
  </style>
</head>

<body>
  <noscript>
    <strong>We're sorry but <%= webpackConfig.name %> doesn't work properly without JavaScript enabled.
        Please enable it to continue.</strong>
  </noscript>
  <div id="app-container">
    <div id="loader-wrapper">
      <div class="loader">
        <div class="inner one"></div>
        <div class="inner two"></div>
        <div class="inner three"></div>
      </div>
      <div id="text">正在加载系统资源，请耐心等待</div>
    </div>
  </div>
  <!-- built files will be auto injected -->
</body>

</html>