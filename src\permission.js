import router from './router';
import store from './store';
import { isCloud } from './config';
import { getToken, setToken, setAppName, Store } from 'sn-base-utils';
const whiteList = ['/login', '/init'];
Store.setStore(store);
const initUserInfo = (to, next) => {
  if (window.__POWERED_BY_QIANKUN__) {
    next();
    return;
  }
  if (store.getters.roles.length === 0 && !to.query.client_id) {
    store.dispatch('GetInfo').then(() => {
      store
        .dispatch('GenerateRoutes', {})
        .then((accessRoutes) => {
          store.commit('changePer');
          console.log(accessRoutes, 'accessRoutes');
          for (const x of accessRoutes) {
            if (x.isFrame == 1) {
              router.addRoute(x);
            }
          }
          next({ ...to, replace: true }); // hack方法 确保addRoutes已完成
        })
        .catch(() => {
          //本地无登录标识
          store.dispatch('LogOut');
        });
    });
  } else {
    next();
  }
};
router.beforeEach((to, from, next) => {
  // 兼容iframe
  if (!window.__POWERED_BY_QIANKUN__) {
    if (to.query.theme) {
      const settings = { theme: `#${to.query.theme}` };
      localStorage.setItem('settings', JSON.stringify(settings));
    }
    if (to.query.application_name) {
      setAppName(to.query.application_name);
    }
    if (to.query.extRoleIds) {
      sessionStorage.setItem('extRoleIds', to.query.extRoleIds);
    }
    if (to.query.isFullscreen) {
      store.dispatch('app/changeIsFullscreen', false);
    }
  }
  if (to.query.access_token) {
    setToken(to.query.access_token);
    delete to.query.access_token;
    next({ ...to });
    return false;
  }

  //本地有token标识
  if (getToken()) {
    store.dispatch('setMeta', to.meta);
    store.commit('SET_DATA_SCOPE', '');
    if (to.path === '/login' && !to.query.client_id) {
      next({ path: '/' });
      return;
    } else {
      initUserInfo(to, next);
    }
  } else {
    if (whiteList.indexOf(to.path) !== -1) {
      next();
    } else {
      if (to.query.access_token) {
        setToken(to.query.access_token);
        location.href = '/';
        return false;
      }
      //本地无登录标识
      if (isCloud) {
        //微服务
        location.href = `${process.env.VUE_APP_BASE_API}/${process.env.VUE_APP_APPLATION_NAME}/oauth/login?redirect_url=${location.href}`;
      } else {
        //单体
        next('/login');
      }
    }
  }
});
