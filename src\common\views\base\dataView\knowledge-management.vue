<template>
  <div>
    <base-tips
      title="知识库权限管理"
      class="mb-10"
    ></base-tips>

    <!-- 操作按钮 -->
    <el-button
      @click="openAddDialog"
      style="margin-bottom: 10px"
      >新增</el-button
    >

    <!-- 表格区域 -->
    <search-table
      v-loading="loading"
      :form="form"
      :fields="fields"
      :columns="columns"
      :data="knowledgeList"
      :query="queryTable"
      :total="total"
      :auto-height="false"
      height="500px"
      :row-height="40"
      :pager="false"
    >
    </search-table>
    <!-- 合并为一个通用弹窗 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="commonDialogVisible"
      width="600px"
      append-to-body
      @open="initCommonDialog"
      @close="pushDialogClose"
    >
      <el-form
        :model="currentForm"
        :rules="currentRules"
        ref="commonForm"
        label-width="120px"
      >
        <el-form-item
          label="知识库名称："
          prop="name"
          v-if="dialogMode === 'add'"
        >
          <el-input v-model="currentForm.name"></el-input>
        </el-form-item>

        <el-form-item
          label="知识库名称："
          v-if="dialogMode === 'permission'"
        >
          <span>{{ currentForm.name }}</span>
        </el-form-item>

        <el-form-item
          label="知识库编码："
          prop="code"
          v-if="dialogMode === 'add'"
        >
          <el-input v-model="currentForm.code"></el-input>
        </el-form-item>

        <el-form-item
          label="知识库编码："
          v-if="dialogMode === 'permission'"
        >
          <span>{{ currentForm.code }}</span>
        </el-form-item>

        <el-form-item
          label="用户权限配置："
          prop="permissions"
        >
          <el-cascader
            :show-all-levels="false"
            v-model="currentForm.permissions"
            ref="permissionCascader"
            :options="cascaderOptions"
            :props="cascaderProps"
            collapse-tags
            clearable
            placeholder=""
            filterable
            :key="cascaderKey"
            v-loading="userTreeLoading"
            style="width: 100%"
            @change="handlePermissionChange"
          ></el-cascader>
        </el-form-item>

        <el-form-item
          label="启停用："
          prop="status"
          v-if="dialogMode === 'add'"
        >
          <el-switch
            v-model="currentForm.status"
            active-value="enabled"
            inactive-value="disabled"
          ></el-switch>
        </el-form-item>
      </el-form>

      <!-- 新增的部门和用户列表区域 -->
      <div
        class="permission-list-container"
        v-if="dialogMode === 'permission'"
      >
        <div class="department-list">
          <el-scrollbar>
            <ul>
              <li
                v-for="dept in departmentList"
                :key="dept.id"
              >
                {{ dept.departmentName }}
              </li>
            </ul>
          </el-scrollbar>
        </div>
        <div class="user-list">
          <el-scrollbar>
            <ul>
              <li
                v-for="user in userList"
                :key="user.id"
              >
                {{ user.userName }}
              </li>
            </ul>
          </el-scrollbar>
        </div>
      </div>
      <span
        slot="footer"
        class="dialog-footer"
      >
        <el-button @click="commonDialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="submitCommonForm"
          >提交</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>
<script>
import {
  getKnowledgeList,
  getOrgList,
  getOrgUserList,
  addKnowledge,
  getKnowledgePermission,
  enableAndDisable,
  subOrgList,
  grantPermission,
} from '@/common/api/data';
export default {
  data() {
    return {
      loading: false,
      knowledgeList: [
        {
          name: '新闻知识库',
          code: 'XXX',
          status: 'enabled',
        },
        {
          name: '党群知识库',
          code: 'XXX',
          status: 'disabled',
        },
      ],
      permissionDialogVisible: false, // 权限配置弹窗显示状态
      addDialogVisible: false, // 新增知识弹窗显示状态
      permissionForm: {
        name: '',
        code: '',
        permissions: [],
      }, // 权限配置表单数据
      commonDialogVisible: false,
      dialogMode: 'add',
      currentForm: {
        name: '',
        code: '',
        permissions: [],
        status: 'enabled',
      }, // 当前表单数据
      addForm: {
        name: '',
        code: '',
        permissions: [],
        status: 'enabled',
      }, // 新增知识表单数据
      addRules: {
        name: [{ required: true, message: '请输入知识库名称', trigger: 'blur' }],
        code: [{ required: true, message: '请输入知识库编码', trigger: 'blur' }],
        permissions: [{ required: true, message: '请选择用户', trigger: 'change' }],
      }, // 新增知识表单验证规则
      permissionOptions: [
        { value: 'option1', label: '选项1' },
        { value: 'option2', label: '选项2' },
      ], // 权限配置选项
      form: {}, // search-table 的 form 数据
      fields: [], // search-table 的字段配置
      columns: [
        { prop: 'knowledgeName', label: '知识名称' },
        { prop: 'knowledgeCode', label: '知识编码' },
        // { prop: 'permissionInfo', label: '权限信息' },
        {
          prop: 'status',
          label: '状态',
          formatter: (row) => (row.status === 'enabled' ? '启用' : '已停用'),
        },
        {
          label: '操作',
          scopedSlots: {
            default: (scope) => {
              return (
                <span>
                  <el-button
                    size="mini"
                    onClick={() => this.configurePermission(scope.row)}
                  >
                    权限配置
                  </el-button>
                  <el-button
                    size="mini"
                    onClick={() => this.toggleStatus(scope.row)}
                  >
                    {scope.row.status === 'enabled' ? '停用' : '启用'}
                  </el-button>
                </span>
              );
            },
          },
        },
      ],
      total: 0, // 数据总条数

      // 新增的权限配置相关数据
      cascaderOptions: [], // 部门树数据
      userSyncTime: [],
      cascaderKey: 0, // Cascader组件key，用于重置组件
      userTreeLoading: false, // 用户树加载状态
      departmentList: [], // 部门列表
      userList: [], // 用户列表
      cascaderProps: {
        multiple: true,
        label: 'orgName',
        value: 'id',
        children: 'children',
        emitPath: false,
        lazy: true,
        lazyLoad: this.lazyLoadUsers,
      },
    };
  },
  computed: {
    dialogTitle() {
      return this.dialogMode === 'add' ? '新增知识' : '权限配置';
    },
    currentRules() {
      return this.dialogMode === 'add' ? this.addRules : {};
    },
  },
  methods: {
    // 打开新增知识弹窗
    openAddDialog() {
      this.dialogMode = 'add';
      this.userSyncTime = [];
      this.currentForm = {
        name: '',
        code: '',
        permissions: [],
        status: 'enabled',
      };
      this.commonDialogVisible = true;
      this.$nextTick(() => {
        // 使用正确的表单引用
        if (this.$refs['commonForm']) {
          this.$refs['commonForm'].clearValidate();
        }
      });
    },
    // 查询表格数据
    async queryTable(params) {
      try {
        this.loading = true;

        // 根据 params 获取数据
        const res = await getKnowledgeList(params);

        this.loading = false;

        if (res && res.code === 200) {
          this.knowledgeList = res.data.map((item) => ({
            ...item,
            // 将后端返回的数字状态转换为前端使用的字符串状态
            status: item.status == 0 ? 'enabled' : 'disabled', // 1是停用，0是启用
          }));
          this.total = res.data.total || res.data.length;
        } else {
          this.$message.error(res.msg || '获取数据失败');
        }
      } catch (error) {
        console.error('获取表格数据失败:', error);
        this.$message.error('获取表格数据失败');
        this.loading = false;
      }
    },
    // 初始化通用弹窗
    async initCommonDialog() {
      // 只在新增模式下获取用户树
      if (this.dialogMode === 'add') {
        await this.getUserTree();
      }
    },

    // 加载用户树
    async getUserTree() {
      this.userTreeLoading = true;

      try {
        const requestData = {
          structType: '0',
          uniqueCode: null,
        };
        const res = await getOrgList(requestData);
        console.log('getOrgList response:', res); // 添加日志
        this.userTreeLoading = false;
        if (res && res.code === 200) {
          // 确保 res 不为 undefined
          // 转换部门数据，添加懒加载标记
          this.cascaderOptions = this.convertToCascaderData(res.data);
        }
      } catch (error) {
        this.userTreeLoading = false;
        console.error('获取部门树失败:', error);
        this.$message.error('获取部门树失败');
      }
    },
    // 递归检查是否有用户节点
    checkHasUsers(nodes) {
      for (const node of nodes) {
        if (node.isUser) {
          console.log('找到用户节点:', node);
          return true;
        }
        if (node.children && this.checkHasUsers(node.children)) {
          return true;
        }
      }
      return false;
    },
    // 转换部门树数据格式

    convertToCascaderData(departments) {
      return departments.map((dep) => {
        // 递归处理子部门
        const childDepartments = dep.children ? this.convertToCascaderData(dep.children) : [];

        return {
          id: dep.id,
          orgName: dep.orgName,
          parentId: dep.parentId,
          children: childDepartments,
          leaf: false, // 标记为非叶子节点，允许展开
          isDepartment: true, // 标记为部门节点
        };
      });
    },
    async configurePermission(row) {
      this.dialogMode = 'permission';

      try {
        // 调用接口获取知识库权限配置
        const res = await getKnowledgePermission(row.id);

        if (res && res.code === 200) {
          // 从接口返回的数据中获取权限信息
          const permissionData = res.data;

          // 设置当前表单数据，只关注 knowledgeCode 和 knowledgeName
          this.currentForm = {
            id: row.id,
            name: permissionData.knowledgeName || row.name,
            code: permissionData.knowledgeCode || row.code,
            permissions: [], // 先初始化为空，后面会设置
            status: row.status,
          };

          // 获取部门列表
          this.departmentList = permissionData.orgs || [];

          // 获取用户列表
          this.userList = permissionData.users || [];

          // 直接在这里获取部门树数据
          await this.getUserTree();
        } else {
          this.$message.error(res.msg || '获取权限配置失败');
          return;
        }
      } catch (error) {
        console.error('获取权限配置失败:', error);
        this.$message.error('获取权限配置失败');
        return;
      }

      this.commonDialogVisible = true;
      this.$nextTick(() => {
        if (this.$refs['commonForm']) {
          this.$refs['commonForm'].clearValidate();
        }
      });
    },
    // 加载权限配置的用户信息
    async loadPermissionUsers(permissionIds) {
      this.userTreeLoading = true;
      try {
        // 获取完整的部门树数据
        const res = await getOrgList();

        if (res.code === 200) {
          // 如果有已配置的权限，需要获取这些用户的具体信息
          if (permissionIds && permissionIds.length > 0) {
            // 暂时使用现有的部门树数据
            this.cascaderOptions = this.convertToCascaderData(res.data);
          } else {
            // 没有已配置的权限，直接使用部门树数据
            this.cascaderOptions = this.convertToCascaderData(res.data);
          }
        }
      } catch (error) {
        console.error('获取权限用户数据失败:', error);
        this.$message.error('获取权限用户数据失败');
      } finally {
        this.userTreeLoading = false;
      }
    },
    // 懒加载用户数据
    async lazyLoadUsers(node, resolve) {
      const { level } = node;

      // 如果是第一层节点（部门），直接返回已有的子节点
      if (level === 0) {
        return resolve(node.data.children || []);
      }

      // 如果是部门节点，需要获取该部门下的子部门和用户
      if (!node.data.isUser && node.data.id) {
        try {
          let childrenDepartments = [];

          // 构造请求参数
          const requestData = {
            structType: '0',
            uniqueCode: null,
          };

          // 如果是加载子部门，需要设置parentId
          if (node.data.id) {
            requestData.parentId = node.data.id;
          }

          // 获取子部门数据
          const departmentRes = await subOrgList(requestData);

          if (departmentRes && departmentRes.code === 200) {
            // 将部门数据转换为 Cascader 可用的格式
            childrenDepartments = (departmentRes.data || []).map((dep) => ({
              id: dep.id,
              orgName: dep.orgName,
              parentId: dep.parentId,
              children: [], // 子部门需要进一步展开
              leaf: false, // 部门节点标记为非叶子节点
              isDepartment: true,
            }));
          }

          // 获取该部门下的用户
          const userRequestData = {
            filter: {
              delFlag: 0,
              orgId: node.data.id,
            },
            page: {
              pageNum: 1,
              pageSize: 1000000,
            },
          };

          const userRes = await getOrgUserList(userRequestData);

          let userNodes = [];
          if (userRes && userRes.code === 200) {
            // 将用户数据转换为 Cascader 可用的格式
            userNodes = (userRes.data.dataList || []).map((user) => ({
              id: user.userId,
              orgName: `${user.userName}（用户）`,
              isUser: true,
              userId: user.userId,
              userName: user.userName,
              userCode: user.userCode,
              orgId: user.orgId,
              leaf: true, // 用户节点标记为叶子节点
            }));
          }

          // 合并子部门和用户
          const children = [...childrenDepartments, ...userNodes];
          resolve(children);
        } catch (error) {
          console.error('获取部门数据失败:', error);
          this.$message.error('获取部门数据失败');
          resolve([]);
        }
      } else {
        // 如果是用户节点或没有子节点，返回空数组
        resolve([]);
      }
    },
    processPermissionSelection(selectedValues) {
      const departmentIds = [];
      const userCodes = [];

      console.log('开始处理权限选择:', selectedValues);

      // 获取级联选择器的选中状态
      const cascaderRef = this.$refs.permissionCascader;
      if (!cascaderRef) {
        console.warn('级联选择器引用不存在');
        return { departmentIds, userCodes };
      }

      // 获取选中的节点信息
      const checkedNodes = cascaderRef.getCheckedNodes();
      console.log('选中的节点:', checkedNodes);

      // 用于记录已经被处理的节点
      const processedNodes = new Set();

      // 按层级排序，优先处理上级部门
      const sortedCheckedNodes = checkedNodes.sort((a, b) => {
        const aLevel = (a.path || []).length;
        const bLevel = (b.path || []).length;
        return aLevel - bLevel;
      });

      // 检查一个部门节点是否被完全选中（基于级联选择器的选中状态）
      const isDepartmentFullyChecked = (departmentNode) => {
        if (!departmentNode.data.isDepartment) {
          return false;
        }

        // 获取该部门下的所有子节点（包括用户和子部门）
        const childrenNodes = checkedNodes.filter((node) => node.data.parentId === departmentNode.data.id);

        // 检查这些子节点是否都被选中
        return childrenNodes.every((node) => checkedNodes.some((checkedNode) => checkedNode.data.id === node.data.id));
      };

      // 递归标记被全选部门包含的所有子节点
      const markChildrenAsProcessed = (departmentId) => {
        checkedNodes.forEach((node) => {
          if (this.isChildOfDepartment(node.data, departmentId)) {
            processedNodes.add(node.data.id);
            // 如果子节点也是部门，递归标记其子节点
            if (node.data.isDepartment) {
              markChildrenAsProcessed(node.data.id);
            }
          }
        });
      };

      // 处理每个选中的节点
      sortedCheckedNodes.forEach((node) => {
        const nodeData = node.data;

        // 如果这个节点已经被上级全选部门包含，跳过
        if (processedNodes.has(nodeData.id)) {
          console.log(`节点 ${nodeData.orgName} 已被上级部门包含，跳过`);
          return;
        }

        if (nodeData.isDepartment) {
          // 如果是部门节点，检查是否全选
          if (isDepartmentFullyChecked(node)) {
            // 部门全选，只添加部门ID
            departmentIds.push(nodeData.id);
            console.log(`部门 ${nodeData.orgName} 被全选，添加部门ID: ${nodeData.id}`);

            // 标记该部门下的所有子节点为已处理
            markChildrenAsProcessed(nodeData.id);
          } else {
            // 部门未全选，不添加部门ID
            console.log(`部门 ${nodeData.orgName} 未全选，不添加部门ID`);
          }
        } else if (nodeData.isUser) {
          // 如果是用户节点，添加用户编码
          if (nodeData.userCode) {
            userCodes.push(nodeData.userCode);
            console.log(`添加用户: ${nodeData.orgName} (${nodeData.userCode})`);
          }
        }
      });

      console.log('最终处理结果:', {
        departmentIds: [...new Set(departmentIds)],
        userCodes: [...new Set(userCodes)],
      });

      return {
        departmentIds: [...new Set(departmentIds)],
        userCodes: [...new Set(userCodes)],
      };
    },
    // 添加辅助方法
    isChildOfDepartment(nodeData, departmentId) {
      // 如果是用户节点，检查其orgId
      if (nodeData.isUser) {
        return nodeData.orgId === departmentId;
      }

      // 如果是部门节点，检查其parentId
      if (nodeData.isDepartment) {
        return nodeData.parentId === departmentId;
      }

      return false;
    },
    // 获取部门下被选中的用户编码
    getSelectedUsersInDepartment(departmentId, checkedNodes) {
      // const userCodes = [];

      // 递归查找该部门下的所有用户
      const findUsersInDepartment = (nodes, targetDeptId) => {
        const users = [];

        const searchInNode = (node) => {
          if (node.data.isUser) {
            // 检查用户是否属于目标部门
            const userParentDept = this.findUserParentDepartment(node.data.id);
            if (userParentDept === targetDeptId) {
              users.push(node.data);
            }
          }

          if (node.children) {
            node.children.forEach((child) => searchInNode(child));
          }
        };

        nodes.forEach((node) => searchInNode(node));
        return users;
      };

      const usersInDept = findUsersInDepartment(checkedNodes, departmentId);
      return usersInDept.map((user) => user.userCode).filter((code) => code);
    },
    // 获取部门下的所有用户（需要考虑懒加载的情况）
    async getAllUsersInDepartment(department) {
      const users = [];

      if (!department) {
        return users;
      }

      // 如果部门的children中没有用户，需要通过API获取
      const hasUsers = department.children && department.children.some((child) => child.isUser);

      if (!hasUsers) {
        // 通过API获取该部门下的用户
        try {
          const requestData = {
            filter: {
              delFlag: 0,
              orgId: department.id,
            },
            page: {
              pageNum: 1,
              pageSize: 1000000,
            },
          };

          const res = await getOrgUserList(requestData);

          if (res && res.code === 200) {
            const apiUsers = (res.data.dataList || []).map((user) => ({
              id: user.userId,
              userId: user.userId,
              userCode: user.userCode,
              orgId: user.orgId,
              userName: user.userName,
            }));
            users.push(...apiUsers);
          }
        } catch (error) {
          console.error('获取部门用户失败:', error);
        }
      } else {
        // 从已有的children中收集用户
        const collectUsers = (dept) => {
          if (dept.children && Array.isArray(dept.children)) {
            dept.children.forEach((child) => {
              if (child.isUser) {
                users.push({
                  id: child.id,
                  userId: child.userId,
                  userCode: child.userCode,
                  orgId: child.orgId,
                  userName: child.userName,
                });
              } else {
                // 递归处理子部门
                collectUsers(child);
              }
            });
          }
        };

        collectUsers(department);
      }

      // console.log(`部门 ${department.orgName} 下的所有用户:`, users);
      return users;
    },

    // 查找用户的父部门ID（从级联选择器的选中节点中查找）
    findUserParentDepartment(userId) {
      // 从级联选择器的选中节点中查找用户
      const cascaderRef = this.$refs.permissionCascader;
      if (!cascaderRef) {
        return null;
      }

      const checkedNodes = cascaderRef.getCheckedNodes();

      // 直接从选中的用户节点中获取orgId
      for (const node of checkedNodes) {
        if (node.data.isUser && node.data.id == userId) {
          // console.log(`找到用户 ${userId} 的部门ID:`, node.data.orgId);
          return node.data.orgId;
        }
      }

      // console.log(`未找到用户 ${userId} 的部门信息`);
      return null;
    },
    // 提交通用表单
    submitCommonForm() {
      if (this.dialogMode === 'add') {
        this.submitAdd();
      } else {
        this.submitPermissionConfig();
      }
    },

    // 修改提交新增方法，支持异步处理
    async submitAdd() {
      this.$refs['commonForm'].validate(async (valid) => {
        if (valid) {
          try {
            // 处理权限选择（现在是异步的）
            const { departmentIds, userCodes } = await this.processPermissionSelection(this.currentForm.permissions);

            console.log('最终处理结果:', {
              departmentIds,
              userCodes,
              原始选择: this.currentForm.permissions,
            });

            // 构造请求数据
            const requestData = {
              id: '',
              knowledgeCode: this.currentForm.code,
              knowledgeName: this.currentForm.name,
              departmentIds: departmentIds,
              userCodes: userCodes,
            };

            console.log('发送请求数据:', requestData);

            // 调用新增知识库接口
            const res = await addKnowledge(requestData);

            if (res && res.code === 200) {
              this.commonDialogVisible = false;
              this.$refs['commonForm'].resetFields();
              this.$message.success('新增知识库成功');

              // 刷新表格数据
              this.queryTable({});
            } else {
              this.$message.error(res.msg || '新增知识库失败');
            }
          } catch (error) {
            console.error('新增知识库失败:', error);
            this.$message.error('新增知识库失败');
          }
        }
      });
    },

    // 生成权限信息显示文本
    generatePermissionInfo(permissions) {
      if (!permissions || permissions.length === 0) {
        return '暂无权限配置';
      }

      return `已选择 ${permissions.length} 项权限`;
    },

    // 提交权限配置
    async submitPermissionConfig() {
      try {
        // 处理权限选择
        const { departmentIds, userCodes } = await this.processPermissionSelection(this.currentForm.permissions);

        // 构造请求数据
        const requestData = {
          departmentIds: departmentIds,
          userCodes: userCodes,
        };

        console.log('发送权限配置请求数据:', requestData);

        // 调用权限配置接口，需要传递知识库ID和请求数据
        const res = await grantPermission(this.currentForm.id, requestData);

        if (res && res.code === 200) {
          // 更新本地数据
          const index = this.knowledgeList.findIndex(
            (item) => item.name === this.currentForm.name && item.code === this.currentForm.code,
          );

          if (index !== -1) {
            this.$set(this.knowledgeList, index, {
              ...this.knowledgeList[index],
              permissions: [...this.currentForm.permissions],
              permissionInfo: this.generatePermissionInfo(this.currentForm.permissions),
            });
          }

          // 关闭弹窗
          this.commonDialogVisible = false;
          this.$message.success('权限配置保存成功');
        } else {
          this.$message.error(res.msg || '权限配置保存失败');
        }
      } catch (error) {
        console.error('权限配置保存失败:', error);
        this.$message.error('权限配置保存失败');
      }
    },
    //关闭dialog
    pushDialogClose() {
      this.form = {};
      // this.userSyncTime = [];
      this.$nextTick(() => {
        // 使用正确的表单引用
        if (this.$refs['commonForm']) {
          this.$refs['commonForm'].clearValidate();
        }
      });
    },
    // 切换状态（启用/停用）
    async toggleStatus(row) {
      const actionText = row.status === 'enabled' ? '停用' : '启用';
      const confirmText = `确定要${actionText}知识库"${row.knowledgeName}"吗？`;
      const newStatus = row.status === 'enabled' ? 'disabled' : 'enabled';
      // 直接使用数字状态值
      const statusValue = newStatus === 'enabled' ? 0 : 1; // 1是停用，0是启用

      try {
        await this.$confirm(confirmText, '确认操作', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        });

        const res = await enableAndDisable(row.id, statusValue);

        if (res && res.code === 200) {
          row.status = newStatus;
          this.$message.success(`${actionText}成功`);
        } else {
          this.$message.error(res.msg || `${actionText}失败`);
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error(`${actionText}知识库失败:`, error);
          this.$message.error(`${actionText}失败`);
        }
      }
    },
    // 权限变化处理方法
    handlePermissionChange(value) {
      console.log('权限选择变化:', value);
      this.currentForm.permissions = value;
    },

    // 更新查找节点方法
    findNodeById(nodes, id) {
      for (const node of nodes) {
        if (node.id == id) {
          // 使用 == 以处理字符串和数字ID的比较
          return node;
        }
        if (node.children) {
          const found = this.findNodeById(node.children, id);
          if (found) return found;
        }
      }
      return null;
    },
  },
};
</script>
<style lang="scss" scoped>
.mb-10 {
  margin-bottom: 10px;
}
.el-table .cell.operation {
  width: 200px !important;
}
.permission-list-container {
  display: flex;
  margin-top: 20px;
}

.department-list,
.user-list {
  flex: 1;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 10px;
  box-sizing: border-box;
  overflow: hidden;
}

.department-list {
  margin-right: 10px;
}

.user-list {
  margin-left: 10px;
}

.department-list ul,
.user-list ul {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-wrap: wrap; /* 允许子元素换行 */
}

.department-list li,
.user-list li {
  padding: 5px 10px;
  margin: 5px;
  background-color: #f5f5f5;
  border-radius: 4px;
  cursor: pointer;
}
</style>
