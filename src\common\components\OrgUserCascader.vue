<template>
  <el-cascader
    :show-all-levels="false"
    v-model="value"
    ref="cascader"
    :options="cascaderOptions"
    :props="props"
    collapse-tags
    clearable
    @change="handleChange"
    filterable
    :key="cascaderKey"
    v-loading="userTreeLogin"
  ></el-cascader>
</template>

<script>
import { getUserTreeRequest, getUserTreeRequest1 } from '@/common/api/data';

export default {
  name: 'OrgUserCascader',
  props: {
    // 接收父组件传递的初始值
    modelValue: {
      type: Array,
      default: () => [],
    },
    // 接收用户同步时间范围
    syncTime: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      value: this.modelValue,
      cascaderOptions: [],
      props: {
        multiple: true,
        label: 'departmentName',
        value: 'departmentId',
        children: 'children',
        emitPath: false,
        lazy: true,
        lazyLoad: this.lazyLoad,
      },
      cascaderKey: 0,
      userTreeLogin: false,
    };
  },
  watch: {
    // 监听父组件传递的同步时间变化
    syncTime: {
      handler(newVal) {
        this.cascaderOptions = [];
        this.cascaderKey += 1;
        // 根据是否有时间范围调整懒加载模式
        if (newVal && newVal.length > 0) {
          this.props = {
            ...this.props,
            lazy: false,
            lazyLoad: () => {},
          };
        } else {
          this.props = {
            ...this.props,
            lazy: true,
            lazyLoad: this.lazyLoad,
          };
        }
        this.getUserTree();
      },
      deep: true,
    },
    // 监听父组件传递的modelValue变化
    modelValue: {
      handler(newVal) {
        console.log(newVal);
        this.value = [...newVal]; // 使用展开运算符创建新数组
        // 当新值为空数组时，调用clearChecked方法清除UI选中状态
        if (!newVal || newVal.length === 0) {
          this.$nextTick(() => {
            this.clearChecked();
          });
        }
      },
      deep: true,
    },
    // 监听值变化，向父组件发出事件
    value(newVal) {
      this.$emit('update:modelValue', newVal);
    },
  },
  mounted() {
    this.getUserTree();
  },
  methods: {
    // 获取用户树数据
    async getUserTree() {
      this.userTreeLogin = true;
      let data = {};
      if (this.syncTime && this.syncTime.length > 0) {
        data = {
          startDate: this.syncTime[0],
          endDate: this.syncTime[1],
        };
      }
      const res = await getUserTreeRequest(data);
      this.userTreeLogin = false;
      if (res.code === 200) {
        this.cascaderOptions = this.convertToCascaderData(res.data);
      }
    },
    // 转换数据为级联选择器格式
    convertToCascaderData(departments) {
      return departments.map((dep) => {
        // 转换用户为"伪部门"节点
        const userNodes = (dep.users || []).map((user) => ({
          departmentId: `${user.userCode}`, // 将departmentId改为userCode
          departmentName: `${user.userName}（用户）`,
          isUser: true,
          userId: user.userId,
          userName: user.userName,
          userCode: user.userCode,
        }));

        // 递归处理子部门
        const childDepartments = dep.children ? this.convertToCascaderData(dep.children) : [];

        return {
          departmentId: dep.departmentId,
          departmentName: dep.departmentName,
          parentId: dep.parentId,
          // 合并子部门和用户节点
          children: [...childDepartments, ...userNodes],
        };
      });
    },
    // 懒加载用户数据
    async lazyLoad(node, resolve) {
      if (!node) return;
      const { value } = node;
      const res = await getUserTreeRequest1(value);
      const nodes = res.data.map((item) => ({
        departmentId: item.userId,
        departmentName: item.userName,
        leaf: 'children',
      }));
      resolve(nodes);
    },
    // 处理选择变化
    handleChange(value) {
      this.$emit('change', value);
    },
    // 清除选中项
    clearChecked() {
      this.$refs.cascader.$refs.panel.clearCheckedNodes();
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-cascader__search-input {
  margin: 2px 0 2px 10px;
}
</style>
