<template>
  <div>
    <base-tips
      title="版本管理"
      class="mb-10"
    ></base-tips>

    <!-- 搜索区域 -->
    <el-card class="search-card mb-10">
      <el-form
        ref="queryForm"
        :inline="true"
        label-width="80px"
        class="app-header"
      >
        <!-- <el-form-item
          label="更新时间"
          prop="dateRange"
        >
          <el-date-picker
            v-model="date"
            @change="dateChange"
            type="datetimerange"
            clearable
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
          >
          </el-date-picker>
        </el-form-item> -->

        <el-form-item
          label="版本号"
          prop="versionNo"
        >
          <el-input
            v-model="searchParams.filter.versionNo"
            placeholder="请输入版本号"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>

        <el-form-item
          label="用户端"
          prop="clientType"
        >
          <el-select
            v-model="searchParams.filter.clientType"
            placeholder="请选择用户端"
            clearable
          >
            <el-option
              label="用户端"
              value="1"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <el-button
        @click="openPushDialog"
        style="margin-left: 28px"
        >新增</el-button
      >
      <el-button @click="handleQuery">搜索</el-button>
      <el-button @click="resetQuery">重置</el-button>
    </el-card>

    <search-table
      v-loading="loading"
      :form="form"
      :fields="fields"
      :columns="columns"
      :data="dataList"
      :query="queryTable"
      :total="total"
      :auto-height="true"
      min-height="200px"
      max-height="600px"
      :row-height="40"
    >
      <!-- 操作列插槽 -->
      <template
        slot="operation"
        slot-scope="scope"
      >
        <el-button
          type="text"
          @click="editRow(scope.row)"
          >编辑</el-button
        >
        <el-button
          type="text"
          @click="deleteRow(scope.row)"
          >删除</el-button
        >
      </template>
    </search-table>

    <!-- 新增/编辑弹窗 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="pushVisible"
      width="30%"
      @close="pushDialogClose"
    >
      <el-form
        ref="form"
        :model="form"
        :rules="formRules"
        label-width="120px"
      >
        <el-form-item
          label="用户端："
          prop="clientType"
        >
          <el-select
            v-model="form.clientType"
            placeholder="请选择"
            :disabled="!edit"
          >
            <el-option
              label="用户端"
              value="1"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="版本号："
          prop="versionNo"
        >
          <el-input
            v-model="form.versionNo"
            placeholder="请输入"
            :disabled="!edit"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="更新时间："
          prop="versionDate"
          :disabled="!edit"
        >
          <el-date-picker
            v-model="form.versionDate"
            type="datetime"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="请选择更新时间"
            style="width: 100%"
            :disabled="!edit"
            default-time="12:00:00"
          />
        </el-form-item>
        <el-form-item
          label="更新内容："
          prop="versionContent"
        >
          <el-input
            v-model="form.versionContent"
            type="textarea"
            placeholder="请输入"
            maxlength="800"
            show-word-limit
          ></el-input>
        </el-form-item>
        <el-form-item
          label="是否提醒："
          prop="reminderEnabled"
        >
          <el-switch
            v-model="form.reminderEnabled"
            :active-value="1"
            :inactive-value="0"
            :disabled="!edit"
          ></el-switch>
        </el-form-item>
      </el-form>
      <span
        slot="footer"
        class="dialog-footer"
      >
        <el-button @click="pushVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="submitForm"
          >提交</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { addVersion, VersionPage, deleteVersion } from '@/common/api/data';

export default {
  data() {
    return {
      loading: false,
      title: '新增版本',
      dataList: [],
      date: '',
      // 查询参数
      searchParams: {
        page: {
          pageNum: 1, // 修改为1，符合分页从1开始的标准
          pageSize: 10,
        },
        filter: {
          versionNo: '',
          clientType: '',
          startTime: '',
          endTime: '',
        },
      },

      fields: [],
      form: {
        clientType: 1,
        versionNo: '',
        versionDate: '',
        versionContent: '',
        reminderEnabled: 0,
      },
      formRules: {
        clientType: [{ required: true, message: '请选择用户端', trigger: 'change' }],
        versionNo: [
          { required: true, message: '请输入版本号', trigger: 'blur' },
          { pattern: /^[^\u4e00-\u9fa5]+$/, message: '版本号不能包含中文', trigger: 'blur' },
        ],
        versionDate: [{ required: true, message: '请选择更新时间', trigger: 'change' }],
        versionContent: [
          { required: true, message: '请输入更新内容', trigger: 'blur' },
          { min: 1, max: 800, message: '更新内容长度必须在 1 到 800 个字符之间', trigger: 'blur' },
        ],
      },
      total: 0,
      pushVisible: false,
      edit: false,
      columns: [
        { prop: 'versionNo', label: '版本号' },
        {
          prop: 'clientType',
          label: '用户端',
          scopedSlots: {
            default: (scope) => {
              return scope.row.clientType === 1 ? '用户端' : '管理端';
            },
          },
        },
        { prop: 'versionContent', label: '更新内容' },
        { prop: 'versionDate', label: '更新时间' },
        {
          prop: 'reminderEnabled',
          label: '是否提醒',
          scopedSlots: {
            default: (scope) => {
              return scope.row.reminderEnabled === 1 ? '是' : '否';
            },
          },
        },
        { prop: 'userCount', label: '版本使用人数' },
        {
          // 操作
          label: '操作',
          scopedSlots: {
            default: (scope) => {
              return (
                <span>
                  <el-button
                    type="text"
                    onClick={() => this.editRow(scope.row)}
                  >
                    编辑
                  </el-button>
                  <el-button
                    type="text"
                    onClick={() => this.deleteRow(scope.row)}
                  >
                    删除
                  </el-button>
                </span>
              );
            },
          },
        },
      ],
    };
  },
  computed: {
    dialogTitle() {
      return this.edit ? '新增版本' : '编辑版本';
    },
  },
  async mounted() {
    this.getList();
  },
  methods: {
    dateChange(date) {
      if (date && date.length === 2) {
        // 确保日期格式正确
        this.searchParams.filter.startTime = date[0] + ' 00:00:00';
        this.searchParams.filter.endTime = date[1] + ' 23:59:59';
      } else {
        // 清除时间筛选条件
        delete this.searchParams.filter.startTime;
        delete this.searchParams.filter.endTime;
      }
    },
    async getList() {
      try {
        this.loading = true;
        const params = {
          page: {
            pageNum: 1,
            pageSize: 10,
          },
          filter: {},
        };

        const res = await VersionPage(params);

        this.loading = false;

        if (res && res.code === 200) {
          this.dataList = res.data.dataList;
          this.total = res.data.totalCount;
        } else {
          this.$message.error(res.msg || '获取数据失败');
        }
      } catch (error) {
        console.error('获取表格数据失败:', error);
        this.$message.error('获取表格数据失败');
        this.loading = false;
      }
    },

    handleQuery() {
      this.searchParams.page.pageNum = 1;

      const filter = {};

      if (this.searchParams.filter.versionNo) {
        filter.versionNo = this.searchParams.filter.versionNo;
      }

      if (this.searchParams.filter.clientType) {
        filter.clientType = this.searchParams.filter.clientType;
      }

      if (this.searchParams.filter.startTime) {
        filter.startTime = this.searchParams.filter.startTime;
      }

      if (this.searchParams.filter.endTime) {
        filter.endTime = this.searchParams.filter.endTime;
      }

      this.searchParams.filter = filter;

      this.queryTable();
    },
    async queryTable(params) {
      try {
        this.loading = true;

        // 使用传入的params中的页码和每页数量，如果没有则使用当前值
        const pageNum = params?.pageNum || this.searchParams.page.pageNum || 1;
        const pageSize = params?.pageSize || this.searchParams.page.pageSize || 10;

        // 更新本地分页参数
        this.searchParams.page.pageNum = pageNum;
        this.searchParams.page.pageSize = pageSize;

        const filter = {};

        if (this.searchParams.filter.versionNo) {
          filter.versionNo = this.searchParams.filter.versionNo;
        }

        if (this.searchParams.filter.clientType) {
          filter.clientType = this.searchParams.filter.clientType;
        }

        if (this.searchParams.filter.startTime) {
          filter.startTime = this.searchParams.filter.startTime;
        }

        if (this.searchParams.filter.endTime) {
          filter.endTime = this.searchParams.filter.endTime;
        }

        const requestParams = {
          page: {
            pageNum: pageNum,
            pageSize: pageSize,
          },
          filter: filter,
        };

        const res = await VersionPage(requestParams);

        this.loading = false;

        if (res && res.code === 200) {
          this.dataList = res.data.dataList;
          this.total = res.data.totalCount;
        } else {
          this.$message.error(res.msg || '获取数据失败');
        }
      } catch (error) {
        console.error('获取表格数据失败:', error);
        this.$message.error('获取表格数据失败');
        this.loading = false;
      }
    },
    resetQuery() {
      this.searchParams = {
        page: { pageNum: 1, pageSize: 10 },
        filter: {},
      };
      this.date = [];
      this.getList();
    },

    openPushDialog() {
      this.pushVisible = true;
      this.edit = true;
      this.$nextTick(() => {
        this.$refs['form']?.clearValidate();
      });
      this.form.clientType = String(this.form.clientType);
    },

    pushDialogClose() {
      this.form = {
        clientType: 1,
        versionNo: '',
        versionContent: '',
        versionDate: '',
        reminderEnabled: 0,
      };
      this.$nextTick(() => {
        this.$refs['form']?.clearValidate();
      });
    },

    submitForm() {
      this.$refs['form'].validate(async (valid) => {
        if (!valid) {
          console.log('error submit!!');
          return false;
        }
        const res = await addVersion(this.form);
        if (res.code !== 200) return this.$modal.msgError(res.message);
        this.$modal.msgSuccess(`${this.edit ? '新增' : '编辑'}成功！`);
        this.pushVisible = false;
        this.queryTable();
      });
    },

    editRow(row) {
      this.pushVisible = true;
      this.edit = false;
      this.form = { ...row };
      this.form.clientType = String(this.form.clientType);
    },

    async deleteRow(row) {
      this.$confirm('确定要删除该记录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          try {
            const res = await deleteVersion([row.id]);
            console.log(res);

            // 统一处理响应对象，获取正确的code和data
            let result = res;
            if (res && res.data && typeof res.data.code !== 'undefined') {
              // 完整响应对象的情况
              result = res.data;
            }

            if (result.code === 200) {
              this.$message({ type: 'success', message: '删除成功!' });
              this.queryTable();
            } else if (result.code === 1000000002) {
              this.$message.error('有使用人数的版本无法删除');
            } else {
              this.$message.error(result.msg || '删除失败');
            }
          } catch (error) {
            console.error('删除失败:', error);
            this.$message.error('删除失败');
          }
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除',
          });
        });
    },
  },
};
</script>

<style lang="scss" scoped>
.mb-10 {
  margin-bottom: 10px;
}
.search-card {
  margin-bottom: 10px;
}
.serch {
  margin-bottom: 10px;
}
</style>
