<template>
  <el-drawer
    title="用户详情"
    :visible.sync="drawer"
    :before-close="handleClose"
    size="40%"
  >
    <div class="info-container">
      <el-descriptions
        class="margin-top"
        :column="2"
        border
      >
        <el-descriptions-item>
          <template slot="label"> 用户姓名 </template>
          {{ info.userName }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label"> 登录名 </template>
          {{ info.userCode }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label"> 手机号码 </template>
          {{ info.phonenumber }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label"> 身份证号码 </template>
          {{ info.idCard }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label"> 邮箱 </template>
          {{ info.email }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label"> 性别 </template>
          {{ info.sex ? (['0', 0].includes(info.sex) ? '男' : '女') : '' }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label"> 状态 </template>
          {{ info.userStatusName }}
        </el-descriptions-item>
      </el-descriptions>
    </div>
  </el-drawer>
</template>
<script>
export default {
  name: 'UserInfo',
  props: {
    info: {
      type: Object,
      default: () => {},
    },
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      drawer: false,
    };
  },
  mounted() {
    this.drawer = this.visible;
    console.log(this.info, '用户');
  },
  methods: {
    handleClose() {
      this.drawer = false;
      this.$emit('update:visible', false);
    },
  },
};
</script>
<style lang="scss" scoped>
.info-container {
  padding: 8px 15px;
}
</style>
