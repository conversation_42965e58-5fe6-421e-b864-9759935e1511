<template>
  <div class="tips-container">
    <slot name="title">{{ title }}</slot>
  </div>
</template>
<script>
export default {
  name: 'BaseTips',
  props: {
    title: {
      type: String,
      default: '',
    },
  },
};
</script>
<style lang="scss" scoped>
.tips-container {
  padding: 10px 20px;
  background-color: #fff;
  border-radius: 4px;
  border-left: 4px solid rgb(103, 194, 58);
  font-weight: 600;
  font-size: 18px;
}
</style>
