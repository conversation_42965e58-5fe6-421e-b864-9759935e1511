<template>
  <el-dropdown
    trigger="click"
    :hide-on-click="false"
    ref="dropdown"
    class="custom-dropdown"
  >
    <el-tooltip
      class="item"
      effect="dark"
      content="角色筛选"
      placement="top"
    >
      <el-button
        size="mini"
        circle
        icon="el-icon-refrigerator"
      />
    </el-tooltip>
    <el-dropdown-menu slot="dropdown">
      <div
        @click="refData"
        style="
          cursor: pointer;
          margin-top: 5px;
          display: flex;
          justify-content: flex-end;
          font-size: 14px;
          color: dodgerblue;
          margin-right: 10px;
        "
      >
        确定
      </div>
      <div style="margin: 10px 0; height: 1px; background: #dcdfe6"></div>
      <el-dropdown-item
        v-for="(item, key) in data"
        :key="key"
      >
        <el-checkbox
          :label="item.uid"
          :key="item.uid"
          :value="item.checked"
          @change="(e) => dataChange(key)"
          >{{ item.roleName }}</el-checkbox
        >
      </el-dropdown-item>
    </el-dropdown-menu>
  </el-dropdown>
</template>
<script>
import { mapMutations } from 'vuex';
export default {
  name: 'FitterRole',
  props: {
    refresh: {
      default: () => {},
      type: Function,
    },
  },
  data() {
    return {
      data: [],
    };
  },
  created() {
    // 显隐列初始默认隐藏列
    for (const i in this.$store.getters.meta.menuAuth) {
      this.data.push({
        ...this.$store.getters.meta.menuAuth[i],
        checked: true,
      });
    }
  },

  methods: {
    ...mapMutations(['SET_DATA_SCOPE']),
    // 搜索
    // 刷新
    refData() {
      const arr = [];
      for (const item of this.data) {
        if (item.checked) {
          arr.push(item.uid);
        }
      }
      this.SET_DATA_SCOPE(arr);
      this.refresh();
      this.$refs.dropdown.hide(); // 隐藏
    },
    // 点击选项
    dataChange(key) {
      const data = this.data;
      const arr = [];
      let checkedNum = 0;
      for (let i = 0; i < data.length; i++) {
        if (data[i].checked) {
          checkedNum++;
        }
      }
      for (let i = 0; i < data.length; i++) {
        if (i == key) {
          if ((data[i].checked && checkedNum > 1) || !data[i].checked) {
            data[i].checked = !data[i].checked;
          }
        }
        arr.push(data[i]);
      }
      this.data = arr;
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .el-transfer__button {
  border-radius: 4px;
  padding: 9px;
  display: block;
  margin-left: 0px;
  font-size: 12px;
  width: 80%;
}
::v-deep .el-transfer__button:first-child {
  margin-bottom: 10px;
}
::v-deep .el-icon-arrow-right {
  margin-left: 5px;
}
::v-deep .el-transfer-panel {
  width: 40%;
}
::v-deep .el-transfer__buttons {
  width: 20%;
  padding: 0;
  display: inline-flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.custom-dropdown {
  margin-right: -10px;
}
</style>
