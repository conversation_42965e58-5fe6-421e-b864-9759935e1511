<template>
  <div class="log-infor-container">
    <div
      v-if="loading"
      class="loading"
    >
      <i class="el-icon-loading"></i> 页面加载中...
    </div>
    <iframe
      v-show="!loading"
      ref="iframe"
      :src="iframeSrc"
      frameborder="0"
      width="100%"
      height="800px"
      title="登录信息"
      @load="onIframeLoad"
    ></iframe>
  </div>
</template>

<script>
export default {
  name: 'LogInfor',
  data() {
    return {
      loading: true,
    };
  },
  computed: {
    iframeSrc() {
      // 获取当前域名
      const currentOrigin = window.location.origin;
      // 将原来的域名替换为当前域名
      return `${currentOrigin}/qaapi/qaassistant/grafana/explore?orgId=1&kiosk`;
    },
  },
  methods: {
    onIframeLoad() {
      this.loading = false;
      console.log('iframe加载完成');
    },
  },
};
</script>

<style scoped>
.log-infor-container {
  width: 100%;
  height: 100%;
  padding: 20px;
  box-sizing: border-box;
}

.log-infor-container iframe {
  border: 1px solid #e6e6e6;
  border-radius: 4px;
}

.loading {
  text-align: center;
  padding: 50px;
  font-size: 16px;
}
</style>
