import Vue from 'vue';
import Vuex from 'vuex';
import {
  app,
  user,
  meta,
  commonGetters,
  tagsView,
  permission,
  settings,
  // Store
} from 'sn-base-layout';
Vue.use(Vuex);
const store = new Vuex.Store({
  modules: {
    app: app(),
    settings: settings(),
    permission: permission(),
    tagsView: tagsView(),
    user: user(),
    meta: meta(),
  },
  getters: { ...commonGetters },
  mutations: {
    changePer(state) {
      const roles = state.user.roles.map((item) => item.roleKey);
      if (!roles.includes('4aadmin') && !roles.includes('admin')) {
        state.permission.sidebarRouters = state.permission.sidebarRouters.filter((route) => {
          return !['/data', '/user'].includes(route.path);
        });
      }
    },
  },
});
if (!window['__TY_Store']) {
  window['__TY_Store'] = store;
  window['store'] = () => store;
}

export default store;
