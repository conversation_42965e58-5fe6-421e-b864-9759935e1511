<template>
  <div>
    <div class="realtime-header mb-10">
      <base-tips
        title="推送管理"
        class="mb-10"
      >
        <div slot="title">
          <span>推送管理</span>
        </div>
      </base-tips>
    </div>
    <card>
      <!-- 查询表单 -->
      <div class="serch mb-10">
        <el-form
          ref="queryForm"
          :inline="true"
          label-width="80px"
          class="app-header"
        >
          <el-form-item
            label="查询时间"
            prop="userName"
          >
            <el-date-picker
              v-model="date"
              @change="dateChange"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item
            label="推送内容"
            prop="userPhone"
          >
            <el-input
              v-model="queryParams.filter.pushTitle"
              placeholder="请输入推送内容"
              clearable
              @keyup.enter.native="getList"
            />
          </el-form-item>

          <el-form-item>
            <el-button
              type="primary"
              @click="handleQuery"
              >搜索</el-button
            >
            <el-button
              type="info"
              @click="resetQuery"
              >重置</el-button
            >
            <el-button @click="downloadFile">导出</el-button>
          </el-form-item>
        </el-form>
        <el-button @click="openPushDialog">新建推送</el-button>
        <!-- <el-button @click="syncUser">同步用户</el-button> -->
        <el-button @click="asyncSyncUser">同步用户</el-button>
        <el-button @click="SyncUseStatus">同步状态</el-button>
      </div>
      <!-- 表格区域 -->
      <el-table
        v-loading="loading"
        :data="dataList"
      >
        <el-table-column
          label="推送时间"
          align="center"
          prop="pushTime"
        >
          <template slot-scope="scope">
            <div>{{ formatTime(scope.row.pushTime) }}</div>
          </template>
        </el-table-column>
        <el-table-column
          label="推送类型"
          align="center"
          prop="msgType"
        >
          <template slot-scope="scope">
            <div>{{ oldmsgTypes[scope.row.msgType] }}</div>
          </template>
        </el-table-column>
        <el-table-column
          label="推送内容(标题)"
          align="center"
          prop="pushTitle"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="scope">
            <div
              style="color: #409eff"
              class="ellipsis pointer"
              @click="openDetail(scope.row)"
            >
              {{ scope.row.pushTitle }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="备注"
          align="center"
          prop="remark"
          :show-overflow-tooltip="true"
        >
        </el-table-column>
        <el-table-column
          label="用户总数"
          align="center"
          prop="totalUserCount"
        >
          <template slot-scope="scope">
            <div
              style="cursor: pointer; color: #409eff"
              @click="openPushlogVisible(scope.row)"
            >
              {{ scope.row.totalUserCount }}
            </div>
          </template>
        </el-table-column>
        <!-- 可以添加操作列 -->
        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
        >
          <template slot-scope="scope">
            <div v-if="scope.row.pushStatus == 0">
              <el-button
                size="mini"
                type="text"
                @click="handlPush(scope.row)"
                >推送
              </el-button>
              <el-button
                size="mini"
                type="text"
                @click="handleCopyPush(scope.row)"
                >复制新增
              </el-button>
              <el-button
                size="mini"
                type="text"
                @click="handleDelete(scope.row)"
                >删除
              </el-button>
            </div>
            <div v-if="scope.row.pushStatus == 1">
              <span style="color: green; margin-right: 10px">已推送</span>
              <el-button
                size="mini"
                type="text"
                @click="handleCopyPush(scope.row)"
                >复制新增
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          background
          style="text-align: right"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="queryParams.page.pageNum"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="queryParams.page.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </card>

    <!-- 新建推送 dialog -->
    <!-- 添加或修改反馈对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="pushVisible"
      @close="pushDialogClose"
      width="500px"
      append-to-body
      :close-on-click-modal="clickfalse"
      :close-on-press-escape="false"
    >
      <el-form
        ref="form"
        :model="form"
        :rules="formRules"
        label-width="120px"
      >
        <el-form-item
          label="推送类型"
          prop="msgType"
        >
          <el-select
            v-model="form.msgType"
            placeholder="请选择"
          >
            <el-option
              v-for="item in msgTypes"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="用户同步时间"
          v-if="!edit"
        >
          <el-date-picker
            style="width: 100%"
            clearable
            v-model="userSyncTime"
            @change="userSyncTimeChange"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item
          label="推送用户"
          prop="userIds"
          v-loading="userTreeLogin"
        >
          <el-cascader
            :show-all-levels="false"
            v-model="form.userIds"
            ref="cascader"
            :options="cascaderOptions"
            :props="props"
            collapse-tags
            clearable
            @change="handleChange"
            filterable
            :key="cascaderKey"
          ></el-cascader>
        </el-form-item>
        <el-form-item
          label="推送标题"
          prop="pushTitle"
        >
          <el-input
            v-model="form.pushTitle"
            placeholder="请输入推送标题"
          />
        </el-form-item>
        <el-form-item
          label="备注"
          prop="remark"
        >
          <el-input
            v-model="form.remark"
            placeholder="请输入备注"
          />
        </el-form-item>
        <el-form-item
          label="推送描述"
          prop="pushDescription"
        >
          <el-input
            type="textarea"
            v-model="form.pushDescription"
            placeholder="请输入推送描述"
          />
        </el-form-item>
        <el-form-item
          label="URL地址"
          prop="url"
        >
          <el-input
            v-model="form.url"
            placeholder="请输入URL地址"
          />
        </el-form-item>
      </el-form>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          type="primary"
          @click="submitForm"
          v-if="!edit"
          >确 定</el-button
        >
        <el-button @click="pushVisible = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 推送日志对话框 -->
    <el-dialog
      title="推送日志"
      :visible.sync="pushlogVisible"
      append-to-body
    >
      <div>
        <el-form :inline="true">
          <el-form-item
            label="推送状态"
            prop="userPhone"
          >
            <el-select
              v-model="queryPushParams.pushUserStatus"
              placeholder="请选择"
              style="width: 140px"
            >
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-button
            type="primary"
            @click="handlePushQuery"
            >搜索</el-button
          >
          <el-button
            type="info"
            @click="resetPushQuery"
            >重置</el-button
          >
          <el-button @click="exportUserPushList">导出</el-button>
        </el-form>
      </div>
      <el-table
        v-loading="loading"
        :data="pushLogList"
      >
        <el-table-column
          label="工号"
          align="center"
          prop="userId"
        />
        <el-table-column
          label="姓名"
          align="center"
          prop="userName"
        />
        <el-table-column
          label="部门"
          align="center"
          prop="orgName"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="推送状态"
          align="center"
          prop="content"
        >
          <template slot-scope="scope">
            <el-tag
              v-if="scope.row.pushUserStatus == 0"
              sizi="mini"
              type="success"
              >推送成功</el-tag
            >
            <el-tag
              v-if="scope.row.pushUserStatus == 1"
              sizi="mini"
              type="info"
              >推送失败</el-tag
            >
          </template>
        </el-table-column>
        <el-table-column
          label="推送完成时间"
          align="center"
          prop="createTime"
          :show-overflow-tooltip="true"
        />
      </el-table>
      <!-- 分页 -->
      <!-- <div class="pagination-container">
        <el-pagination
          background
          style="text-align: right"
          @size-change="handlePushSizeChange"
          @current-change="handlePushCurrentChange"
          :current-page="queryParams.pageNum"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="queryParams.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div> -->
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button @click="pushlogVisible = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getUserTreeRequest,
  getUserTreeRequest1,
  addPushRequest,
  getPushListRequest,
  doPushRequest,
  getCardListRequest,
  getPushLogListRequest,
  exportPushListRequest,
  exportPushLogListRequest,
  syncUserRequest,
  deletePushRequest,
  getSyncStatusRequest,
  triggerSyncRequest,
  getIdsTreeRequest,
} from '@/common/api/data';
import card from '@/common/components/card/card.vue';

import { download } from '@/common/utils/index';
export default {
  components: { card },
  data() {
    return {
      clickfalse: false,
      title: '新建推送',
      edit: false,
      loading: false,
      date: '', //选择的时间
      // 查询参数
      queryParams: {
        page: {
          pageNum: 0,
          pageSize: 10,
        },
        filter: {
          startTime: '',
          endTime: '',
          pushTitle: '',
        },
      },
      dataList: [], //主列表
      total: 0, //主total
      //推送日志  查询参数
      queryPushParams: {
        pushUserStatus: '',
      },
      currentId: '', //当前选择的id
      options: [
        {
          value: '0',
          label: '成功',
        },
        {
          value: '1',
          label: '失败',
        },
      ],
      pushLogList: [], //推送日志列表
      value: '',
      pushVisible: false, //新增推送对话框
      pushlogVisible: false, //推送日志对话框
      oldmsgTypes: [],
      msgTypes: [
        {
          value: 'text',
          label: '文本',
        },
        {
          value: 'image',
          label: '图片',
        },
      ],
      form: {
        //新增表单
        msgType: '', //推送类型
        userIds: '', //推送到的用户
        pushTitle: '', //推送标题
        pushDescription: '', //推送描述
        remark: '', //备注
        url: '', //url
      },
      formRules: {
        msgType: [{ required: true, message: '请选择推送类型', trigger: 'change' }],
        userIds: [{ required: true, message: '请选择推送用户', trigger: 'change' }],
        pushTitle: [{ required: true, message: '请输入推送标题', trigger: 'blur' }],
        pushDescription: [{ required: true, message: '请输入推送描述', trigger: 'blur' }],
        url: [{ required: true, message: '请输入推送链接', trigger: 'blur' }],
      },
      realtimeText: '',
      onlineUser: 456,
      userOptions: [], // 存储用户选项
      cascaderOptions: [], // 转换后的数据放在这里
      props: {
        multiple: true,
        label: 'departmentName',
        value: 'departmentId',
        children: 'children',
        emitPath: false,
        lazy: true,
        lazyLoad: this.lazyLoad,
      },
      cascaderKey: 0,
      userSyncTime: [], // 用户同步时间
      userTreeLogin: false,
    };
  },
  async mounted() {
    // 初始加载一些用户数据
    this.getList();
    this.getCardList();
  },
  methods: {
    async getList() {
      this.loading = true;
      const res = await getPushListRequest(this.queryParams);
      this.loading = false;
      if (res.code !== 200) return this.$message.error(res.message);
      this.dataList = res.data.dataList;
      this.total = res.data.totalCount;
      console.log(res);
    },
    // 获取类型树
    async getCardList() {
      const res = await getCardListRequest();
      if (res.code !== 200) return this.$message.error(res.message);
      const transformedData = res.data.map((item) => {
        const key = Object.keys(item)[0]; // 获取对象的第一个键
        return {
          value: key,
          label: item[key],
        };
      });
      this.oldmsgTypes = res.data.reduce((acc, item) => {
        // 获取对象的第一个键（假设每个对象只有一个键值对）
        const key = Object.keys(item)[0];
        const value = item[key];
        // 将键值对添加到累积对象中
        acc[key] = value;
        return acc;
      }, {});
      this.msgTypes = transformedData;
    },
    // 获取用户树数据
    async getUserTree() {
      this.userTreeLogin = true;
      //this.userSyncTime 可能为null
      let data = {};
      if (this.userSyncTime) {
        data = {
          startDate: this.userSyncTime[0],
          endDate: this.userSyncTime[1],
        };
      }

      const res = await getUserTreeRequest(data);
      this.userTreeLogin = false;
      console.log(res);
      if (res.code === 200) {
        // this.userOptions = res.data;
        // 假设你已经将原始数据赋值给 rawDepartmentData
        this.cascaderOptions = this.convertToCascaderData(res.data);
      }
    },
    // 根据部门id获取用户
    async getUserList(departmentId) {
      const res = await getUserTreeRequest1({
        departmentId: departmentId,
      });
      if (res.code === 200) {
        this.userOptions = res.data;
      }
    },
    convertToCascaderData(departments) {
      return departments.map((dep) => {
        // 转换用户为“伪部门”节点
        const userNodes = (dep.users || []).map((user) => ({
          departmentId: `${user.userId}`, // 加前缀以区分用户和部门
          departmentName: `${user.userName}（用户）`,
          isUser: true, // 标记为用户
          userId: user.userId,
          userName: user.userName,
          userCode: user.userCode,
          // 不设置 children，这样这个节点就是叶子节点
        }));

        // 递归处理子部门
        const childDepartments = dep.children ? this.convertToCascaderData(dep.children) : [];

        return {
          departmentId: dep.departmentId,
          departmentName: dep.departmentName,
          parentId: dep.parentId,
          // 合并子部门和用户节点
          children: [...childDepartments, ...userNodes],
        };
      });
    },
    handleChange(value) {
      console.log('选中路径:', value);

      // 最后一项是选中的节点 ID
      //value 是一个二数组
      // const selectedId = value[value.length - 1];
      // const selectedId = value.map((item) => {
      //   return item[item.length - 1];
      // });
      // console.log(selectedId);
      // this.form.userIds = selectedId;
    },
    //打开 新增推送 dialog框
    openPushDialog() {
      this.props = {
        ...this.props,
        lazy: true,
        lazyLoad: this.lazyLoad,
      };
      // 新逻辑 获取用户树
      this.getUserTree();

      this.pushVisible = true;
      this.edit = false;
      this.$nextTick(() => {
        this.$refs['form'].clearValidate();
      });
    },
    resetForm() {
      this.$refs['form'].resetFields();
      this.$refs['cascader'].$refs.panel.clearCheckedNodes();
      this.$refs['form'].clearValidate();
    },
    //新增推送 dialog框确定
    submitForm() {
      this.$refs['form'].validate(async (valid) => {
        if (!valid) {
          console.log('error submit!!');
          return false;
        }

        // 组装数据
        this.form.userIds = this.form.userIds.join('|');
        const res = await addPushRequest(this.form);

        if (res.code !== 200) return this.$modal.msgError(res.message);
        this.$modal.msgSuccess('新增成功！');
        this.props = {
          ...this.props,
          lazy: true,
          lazyLoad: this.lazyLoad,
        };
        this.getList();
        this.resetForm();
        this.pushVisible = false;
        console.log(res);
      });
    },
    // 查询时间选择事件
    dateChange(date) {
      this.queryParams.filter.startTime = date[0] + ' 00:00:00';
      this.queryParams.filter.endTime = date[1] + ' 23:59:59';
    },
    //搜搜按钮点击
    handleQuery() {
      this.queryParams.page.pageNum = 1;

      this.getList();
    },
    //重置搜索
    resetQuery() {
      this.queryParams = {
        page: { pageNum: 1, pageSize: 10 },
        filter: {},
      };
      this.date = [];
      this.getList();
    },
    //推送按钮
    handlPush(row) {
      const id = row.id;
      this.$confirm('推送前请检查用户总数是否和实际一致以及是否是您本次推送目标', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          const res = await doPushRequest({ id });
          if (res.code !== 200) return this.$message.error(res.msg);
          this.$message.success(res.msg);
          this.getList();
          console.log(res);
        })
        .catch(() => {});
    },
    //复制新增
    handleCopyPush(row) {
      this.title = '新增推送';
      this.pushVisible = true;
      this.edit = false;
      this.$nextTick(() => {
        this.$refs['form'].clearValidate();
      });
      this.form = {
        msgType: row.msgType,
        userIds: row.userIds.split('|'),
        pushTitle: row.pushTitle,
        pushDescription: row.pushDescription,
        remark: row.remark,
        url: row.url,
      };
    },
    //根据id 删除
    handleDelete(row) {
      // console.log(row);
      const id = row.id;
      console.log(id);
      this.$confirm('确定要删除吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          const res = await deletePushRequest([id]);
          if (res.code !== 200) return this.$message.error(res.msg);
          this.$message.success('删除成功！');
          this.getList();
          console.log(res);
        })
        .catch(() => {});
    },
    //分页器size改变
    handleSizeChange(val) {
      this.queryParams.page.pageSize = val;
      this.getList();
    },
    //分页器改变
    handleCurrentChange(val) {
      this.queryParams.page.pageNum = val;
      this.getList();
    },
    // 打开用户列表  dialog
    async openPushlogVisible(row) {
      this.title = '新增推送';
      this.pushlogVisible = true;
      this.currentId = row.id;
      this.getPushList();
      console.log(row.id);
    },
    //获取pushlist
    async getPushList() {
      this.loading = true;
      if (this.queryPushParams.pushUserStatus == '') {
        delete this.queryPushParams.pushUserStatus;
      }
      const res = await getPushLogListRequest(this.currentId, this.queryPushParams);
      this.loading = false;
      if (res.code != 200) return this.$message.error(res.message);
      this.pushLogList = res.data;
      console.log(res);
    },
    async openDetail(row) {
      this.props = {
        ...this.props,
        lazy: false,
        lazyLoad: () => {},
      };
      this.pushVisible = true;
      this.userTreeLogin = true;
      this.title = '查看推送';
      this.edit = true;
      console.log(row);

      // 获取部门树数据
      const res = await getUserTreeRequest();
      console.log('部门树数据:', res);

      // 获取用户数据
      const res1 = await getIdsTreeRequest({ userIds: row.userIds });
      console.log('用户数据:', res1);

      // 合并部门和用户数据
      if (res.code === 200 && res1.code === 200) {
        const departmentData = res.data;
        const userData = res1.data;

        // 1. 创建部门ID到部门对象的映射，方便快速查找
        const departmentMap = new Map();

        // 递归函数构建部门映射
        const buildDepartmentMap = function (departments) {
          departments.forEach((dept) => {
            // 初始化users数组（如果不存在）
            if (!dept.users) dept.users = [];
            // 存储到映射中
            departmentMap.set(dept.departmentId, dept);
            // 递归处理子部门
            if (dept.children && dept.children.length > 0) {
              buildDepartmentMap(dept.children);
            }
          });
        };

        // 构建部门映射
        buildDepartmentMap(departmentData);

        // 2. 遍历所有用户，将他们添加到对应的部门
        userData.forEach((user) => {
          const departmentId = user.mainDepartment;
          if (departmentMap.has(departmentId)) {
            const department = departmentMap.get(departmentId);
            department.users.push(user);
          } else {
            console.warn(`未找到部门ID: ${departmentId}，用户${user.userName}(${user.userCode})无法添加到部门`);
          }
        });

        // 处理完成后的部门数据包含了对应用户
        console.log('合并后的部门数据:', departmentData);
        this.userTreeLogin = false;
        // 可以将合并后的部门数据赋值给组件变量供后续使用
        this.cascaderOptions = this.convertToCascaderData(departmentData);
        this.departmentWithUsers = departmentData;
      }

      const userIds = row.userIds.split('|');

      this.form = {
        msgType: row.msgType,
        userIds: userIds,
        pushTitle: row.pushTitle,
        pushDescription: row.pushDescription,
        remark: row.remark,
        url: row.url,
      };
    },
    //时间格式化
    formatTime(time) {
      if (!time) return '';
      const date = new Date(time);
      return date.toLocaleDateString();
    },
    //推送的搜索按钮事件
    handlePushQuery() {
      this.getPushList();
    },
    //推送列表重置的按钮事件
    resetPushQuery() {
      this.queryPushParams = {
        pushUserStatus: '',
      };
      this.getPushList();
    },
    //列表导出文件
    async downloadFile() {
      const params = {
        startTime: this.queryParams.filter.startTime,
        endTime: this.queryParams.filter.endTime,
        pushTitle: this.queryParams.filter.pushTitle,
      };
      const res = await exportPushListRequest(params);
      if (res && res.status === 200) {
        download(res.data, '消息推送-详细数据列表');
      }
    },
    // 用户推送列表导出
    async exportUserPushList() {
      const params = {
        pushUserStatus: this.queryPushParams.pushUserStatus,
      };
      const res = await exportPushLogListRequest(this.currentId, params);
      if (res && res.status === 200) {
        download(res.data, '消息推送详情-详细数据列表');
      }
    },
    //同步用户按钮
    async syncUser() {
      const loading = this.$loading({
        lock: true,
        text: '正在同步中...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)',
      });

      const res = await syncUserRequest();
      setTimeout(() => {
        loading.close();
      }, 500);
      if (res && res.status === 200) {
        this.$message.success('同步成功');
      }
    },
    //关闭dialog
    pushDialogClose() {
      this.form = {};
      this.userSyncTime = [];
      this.$nextTick(() => {
        this.$refs['form'].clearValidate();
      });
    },
    // 用户同步时间段选择
    userSyncTimeChange(date) {
      console.log(date);
      this.cascaderOptions = [];
      this.cascaderKey += 1;
      //重置Cascader 组件
      if (date) {
        this.props = {
          ...this.props,
          lazy: false,
          lazyLoad: () => {},
        };
      } else {
        this.props = {
          ...this.props,
          lazy: true,
          lazyLoad: this.lazyLoad,
        };
      }
      this.getUserTree();
    },
    //异步加载用户
    async lazyLoad(node, resolve) {
      if (!node) return;
      const { value } = node;
      console.log(value);

      const res = await getUserTreeRequest1(value);
      const nodes = res.data.map((item) => ({
        departmentId: item.userId,
        departmentName: item.userName,
        leaf: 'children',
      }));
      resolve(nodes);
      console.log(res);
    },
    // 异步同步用户
    async asyncSyncUser() {
      const res = await getSyncStatusRequest();
      console.log(res);
      //res.data 有可能为空

      if (res.data.status === 'no_start' || res.data.status !== 'RUNNING') {
        this.$message.success('开始同步任务~');
        const res1 = await triggerSyncRequest();
        this.$message.success('部门与用户同步任务已提交');
        console.log(res1);
      } else {
        this.$message.info('同步任务正在运行中，请稍后再试');
      }
    },
    // 同步状态
    async SyncUseStatus() {
      const res = await getSyncStatusRequest();
      if (res.data.status === 'RUNNING') {
        this.$message.info('同步任务正在运行中，请稍后再试');
        return;
      }
      this.$message.success(res.data.message);
    },
  },
};
</script>
<style lang="scss" scoped>
.top-container {
  gap: 10px;

  // .top-item {
  //   width: 25%;
  //   height: 100px;
  // }
}

.mb-10 {
  margin-bottom: 10px;
}

.realtime-info {
  color: #909399;
  margin-left: 10px;
  font-size: 14px;
  vertical-align: middle;
}
.chartBox {
  overflow: hidden;
  margin-top: 10px;
  .chartBoxContent {
    width: calc(50% - 5px);
    float: left;
    &:nth-child(odd) {
      margin-right: 10px;
    }
  }
}
.ellipsis {
  white-space: nowrap; /* 防止文本换行 */
  overflow: hidden; /* 隐藏溢出的内容 */
  text-overflow: ellipsis; /* 显示省略号 */
}
.pointer {
  cursor: pointer;
}
::v-deep .el-cascader__search-input {
  margin: 2px 0 2px 10px;
}
</style>
