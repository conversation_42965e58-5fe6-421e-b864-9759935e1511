module.exports = {
  root: true,
  env: {
    node: true,
  },
  extends: ['plugin:vue/essential', 'eslint:recommended', 'plugin:prettier/recommended'],
  parserOptions: {
    parser: '@babel/eslint-parser',
  },
  rules: {
    'no-var': 'error',
    'prefer-const': 'error',
    // 禁止变量声明与外层作用域的变量同名
    'no-shadow': 'error',
    // 禁止在变量定义之前使用它们
    'no-use-before-define': 'error',
    // 禁止可以在有更简单的可替代的表达式时使用三元操作符
    'no-unneeded-ternary': 'error',
    // 禁止重复模块导入
    'no-duplicate-imports': 'error',
    'no-unused-vars': ['error', { vars: 'all', args: 'after-used', ignoreRestSiblings: false }],
    'vue/require-v-for-key': ['error'],
    quotes: ['error', 'single'],
    eqeqeq: ['off', 'always'],
    'vue/multi-word-component-names': 'off',
  },
};
