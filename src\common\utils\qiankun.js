import { registerMicroApps, start, initGlobalState } from 'qiankun';
import { theme } from '../../config';
import store from '@/store';

export const actionsQiankun = initGlobalState(() => {
  return {
    theme: localStorage.getItem('theme') || theme,
  };
});

export const startQiankun = () => {
  //解决qiankun路由问题
  window.Vue2 = window.window.Vue;
  window.window.Vue = null;
  // 上线启用
  const microApps = store.getters.microApps.map((item) => ({
    ...item,
    container: '#qiankunContainer',
    props: {
      remoteRouters: store.getters.remoteRouters.filter((i) => i.clientCode == item.name),
    },
  }));
  registerMicroApps(microApps);
  start({
    prefetch: 'all',
  });
  actionsQiankun.setGlobalState({
    theme: localStorage.getItem('theme') || theme,
  });
};
