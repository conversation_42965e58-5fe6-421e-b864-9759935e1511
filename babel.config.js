module.exports = {
  compact: false,
  presets: [
    '@vue/cli-plugin-babel/preset',
    [
      '@babel/preset-env',
      {
        targets: {
          edge: '17',
          firefox: '60',
          chrome: '67',
          safari: '11.1',
          ie: '11',
        },
        useBuiltIns: 'entry',
        corejs: {
          version: '3.32.1',
          proposals: true,
        },
      },
    ],
  ],
  plugins: [
    [
      'babel-plugin-root-import',
      {
        paths: [
          {
            rootPathPrefix: '@cms',
            rootPathSuffix: 'node_modules/sn-cms-v2/src',
          },
        ],
      },
    ],
  ],
};
