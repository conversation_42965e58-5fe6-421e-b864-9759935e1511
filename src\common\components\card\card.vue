<template>
  <div
    class="card-container"
    :class="{
      'card-radius': radius,
      'card-shadow': shadow,
    }"
  >
    <slot></slot>
  </div>
</template>
<script>
export default {
  name: 'BaseCard',
  props: {
    radius: {
      type: [<PERSON>olean, String],
      default: true,
    },
    shadow: {
      type: [<PERSON><PERSON><PERSON>, String],
      default: true,
    },
  },
};
</script>
<style lang="scss" scoped>
.card-container {
  padding: 10px;
  background: #fff;
}
.card-radius {
  border-radius: 6px;
}
.card-shadow {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
</style>
