<template>
  <div class="svgBox">
    <svg
      :class="svgClass"
      aria-hidden="true"
      v-on="$listeners"
    >
      <use :xlink:href="iconName" />
    </svg>
  </div>
</template>

<script>
export default {
  name: 'SvgIcon',
  props: {
    iconClass: {
      type: String,
      required: true,
    },
    className: {
      type: String,
      default: '',
    },
  },
  computed: {
    iconName() {
      return `#icon-${this.iconClass}`;
    },
    svgClass() {
      if (this.className) {
        return 'svg-icon ' + this.className;
      } else {
        return 'svg-icon';
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.svgBox {
  display: inline-flex;
  align-items: center;
}
</style>
