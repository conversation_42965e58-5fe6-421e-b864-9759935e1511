import layoutIndex from '@common/layoutView/LayoutIndex';

/**
 * Note: 路由配置项
 *
 * hidden: true                     // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面
 * alwaysShow: true                 // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
 *                                  // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面
 *                                  // 若你想不管路由下面的 children 声明的个数都显示你的根路由
 *                                  // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由
 * redirect: noRedirect             // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'               // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * query: '{"id": 1, "name": "ry"}' // 访问路由的默认传递参数
 * roles: ['admin', 'common']       // 访问路由的角色权限
 * permissions: ['a:a:a', 'b:b:b']  // 访问路由的菜单权限
 * menuCode: 'system'               // 服务节点必填 乾坤使用，单体不需要
 * menuType: 'M'                    // 目录必填乾坤使用，单体不需要
 * meta : {
    noCache: true                   // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
    title: 'title'                  // 设置该路由在侧边栏和面包屑中展示的名字
    icon: 'svg-name'                // 设置该路由的图标，对应路径src/assets/icons/svg
    breadcrumb: false               // 如果设置为false，则不会在breadcrumb面包屑中显示
    activeMenu: '/system/user'      // 当路由设置了该属性，则会高亮相对应的侧边栏。
  }
 */
const constantRoutes = [
  {
    path: '/redirect',
    component: layoutIndex,
    hidden: true,
    children: [
      {
        // path: "/redirect/:path(.*)",
        path: '/redirect/middleware',
        component: () => import('@common/views/base/redirectView'),
      },
    ],
  },
  {
    path: '/',
    component: layoutIndex,
    redirect: 'index',
    children: [
      {
        path: 'index',
        name: 'Index',
        component: (resolve) => require(['@common/views/base/indexView/index'], resolve),
        meta: { title: '首页', icon: 'sn-menu-shouye', affix: true },
      },
    ],
  },
  // {
  //   path: '/data',
  //   name: 'Data',
  //   redirect: '/qaassistant/data/real',
  //   component: layoutIndex,
  //   meta: { title: '数据迭代与分析', icon: 'sn-menu-biaodanguanli', affix: false },
  //   children: [
  //     {
  //       path: '/qaassistant/data/real',
  //       name: 'Real',
  //       component: (resolve) => require(['@common/views/base/dataView/real-data'], resolve),
  //       meta: { title: '实时数据', icon: 'sn-menu-zaixianbiaodan', affix: false },
  //       roles: ['admin', 'common'],
  //     },
  //     {
  //       path: '/qaassistant/data/pointsManagement',
  //       name: 'pointsManagement',
  //       component: (resolve) => require(['@common/views/base/dataView/points-management'], resolve),
  //       meta: { title: '积分管理', icon: 'sn-menu-renwuguanli', affix: false },
  //       roles: ['admin'],
  //     },
  //     {
  //       path: '/qaassistant/data/feedbackManagement',
  //       name: 'feedbackManagement',
  //       component: (resolve) => require(['@common/views/base/dataView/feedback-management'], resolve),
  //       meta: { title: '反馈管理', icon: 'sn-menu-yibanrenwu', affix: false },
  //       roles: ['admin'],
  //     },
  //     {
  //       path: '/qaassistant/data/chat',
  //       name: 'Chat',
  //       component: (resolve) => require(['@common/views/base/dataView/user-chat'], resolve),
  //       meta: { title: '用户会话统计', icon: 'sn-menu-xiaoximoban', affix: false },
  //       roles: ['admin'],
  //     },
  //     {
  //       path: '/qaassistant/data/feed',
  //       name: 'Feed',
  //       hidden: true,
  //       component: (resolve) => require(['@common/views/base/dataView/user-feed'], resolve),
  //       meta: { title: '用户反馈', icon: 'sn-menu-tongzhigonggao', affix: false },
  //       roles: ['admin'],
  //     },
  //     {
  //       path: '/qaassistant/data/messagePushManagement',
  //       name: 'messagePushManagement',
  //       component: (resolve) => require(['@common/views/base/dataView/message-push-management'], resolve),
  //       meta: { title: '消息推送管理', icon: 'sn-menu-xiaoxirizhi', affix: false },
  //       roles: ['admin'],
  //     },
  //     {
  //       path: '/qaassistant/data/function',
  //       name: 'Feed',
  //       component: (resolve) => require(['@common/views/base/dataView/function'], resolve),
  //       meta: { title: '功能使用统计', icon: 'sn-menu-wodedaiban', affix: false },
  //       roles: ['admin'],
  //     },
  //     {
  //       path: '/qaassistant/data/versioning',
  //       name: 'Version',
  //       component: (resolve) => require(['@common/views/base/dataView/versioning'], resolve),
  //       meta: { title: '版本管理', icon: 'sn-menu-wodedaiban', affix: false },
  //       roles: ['admin'],
  //     },
  //     {
  //       path: '/qaassistant/data/knowledge-management',
  //       name: 'Knowledge',
  //       component: (resolve) => require(['@common/views/base/dataView/knowledge-management'], resolve),
  //       meta: { title: '知识权限管理', icon: 'sn-menu-wodedaiban', affix: false },
  //       roles: ['admin'],
  //     },
  //   ],
  // },
  // {
  //   path: '/user',
  //   component: layoutIndex,
  //   redirect: '/qaassistant/user/list',
  //   children: [
  //     {
  //       path: '/qaassistant/user/list',
  //       name: 'list',
  //       component: (resolve) => require(['@common/views/base/userView/user'], resolve),
  //       meta: { title: '用户管理', icon: 'sn-menu-yonghuguanli', affix: false },
  //       roles: ['admin'],
  //     },
  //   ],
  // },
  {
    path: '/account',
    component: layoutIndex,
    redirect: 'index',
    children: [
      {
        name: 'userCenter',
        path: '/account/userCenter',
        hidden: true,
        component: (resolve) => require(['@common/views/userCenter/index'], resolve),
        meta: {
          title: '个人中心',
          icon: 'table',
          noCache: false,
          link: null,
        },
      },
    ],
  },
  {
    path: '/login',
    component: (resolve) => require(['@common/views/base/loginView/index'], resolve),
    name: 'loginView',
    hidden: true,
  },
  {
    path: '/401',
    component: (resolve) => require(['@common/views/base/401/index'], resolve),
    name: '401',
    hidden: true,
  },
  {
    path: '/404',
    component: (resolve) => require(['@common/views/base/404/index'], resolve),
    name: '404',
    hidden: true,
  },
  {
    path: '*',
    component: (resolve) => require(['@common/views/base/404/index'], resolve),
  },
];

export default constantRoutes;
