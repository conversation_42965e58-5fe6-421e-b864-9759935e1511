.el-menu {
  border: none !important;

  .svg-icon {
    width: 16px;
    height: 16px;
    fill: currentColor;
    margin-right: 10px;
    vertical-align: middle;
  }
  .sn-icon i {
    width: 16px;
    height: 16px;
    font-size: 16px;
    vertical-align: baseline;
  }
}

#sideBar {
  width: 220px;
  height: 100%;
  position: relative;
  transition: all 0.3s;

  .Collapse {
    position: absolute;
    height: 60px;
    width: 4px;
    background: #fff;
    margin-top: -30px;
    top: 50%;
    right: -4px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;

    .icon {
      font-size: 6px;
      color: #ccc;
    }
  }

  .el-scrollbar {
    height: 100%;
  }
}

// horizantal
#sideBarHorizantal {
  position: relative;
  transition: all 0.3s;

  .Collapse {
    position: absolute;
    height: 60px;
    width: 4px;
    background: #fff;
    margin-top: -30px;
    top: 50%;
    right: -4px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;

    .icon {
      font-size: 6px;
      color: #ccc;
    }
  }

  .el-scrollbar {
    height: 100%;
  }
}

.hideSidebar {
  width: 64px !important;

  .el-submenu__icon-arrow {
    right: 13px;
    // margin-top: -6px;
  }

  .submenu-title-noDropdown {
    padding: 0 !important;
    position: relative;

    .el-tooltip {
      padding: 0 !important;
      display: inline-flex !important;
      align-items: center;
      justify-content: center;
    }
  }

  .el-submenu {
    overflow: hidden;

    & > .el-submenu__title {
      padding: 0 !important;
      justify-content: center;
    }
  }

  .el-menu--collapse {
    .el-submenu {
      & > .el-submenu__title {
        display: flex;
        align-items: center;
        justify-content: center;
        & > .title {
          height: 0;
          width: 0;
          overflow: hidden;
          visibility: hidden;
          display: inline-block;
        }
      }
    }
  }
}

.el-menu--horizontal .el-menu .el-menu-item,
.el-menu--horizontal .el-submenu__title {
  background: var(--theme-color) !important;
}

.el-submenu__title,
.el-menu-item {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding-right: 34px !important;
}
