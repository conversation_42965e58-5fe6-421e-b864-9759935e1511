import { formatDate } from 'element-ui/lib/utils/date-util';
export const loadView = (view) => {
  const str = view.substr(0, 1);
  let path = view;
  if (str == '/') {
    path = view.slice(1);
  }
  if (process.env.NODE_ENV === 'development') {
    return (resolve) => require([`@/${path}`], resolve);
  } else {
    return () => import(`@/${path}`);
  }
};
// 初始化菜单使用获取项目下文件
export const readLocalSrcFiles = () => {
  return require.context('@/', true, /routerConfig.js|index.vue/);
};

/**
 *@description 判断一个字符串是否为JSON字符串
 *@
 * @param {string} str
 * @return {boolean}
 */
export const isJsonStr = (str) => {
  if (typeof str === 'string') {
    try {
      const obj = JSON.parse(str);
      if (typeof obj === 'object' && obj) {
        return true;
      }
      return false;
    } catch {
      return false;
    }
  } else {
    return false;
  }
};

/**
 *
 * 这是前端接受后端数据流进行下载文件的方法
 * @param {Blob} data  后端返回的二进制数据流
 * @param {string} name  下载的文件名称
 */
export const download = function (data, name) {
  /* 新方法创建Blob 对象
              在新的方法中直接可以通过 Blob() 的构造函数来创建了。
              构造函数，接受两个参数，第一个为一个数据序列，可以是任意格式的值，
              例如，任意数量的字符串，Blobs 以及 ArrayBuffers。
              第二个参数，是一个包含了两个属性的对象，其两个属性分别是：type -- MIME的类型。决定文件下载的类型;
                         另一个为endings -- 决定 append() 的数据格式，（数据中的 \n 如何被转换）
                         可以取值为 "transparent" 或者 "native"（t* 的话不变，n* 的话按操作系统转换；t* 为默认）
               */
  const url = window.URL.createObjectURL(data);
  const ele = document.createElement('a');
  ele.style.display = 'none';
  ele.href = url;
  // 下载文件的名称
  const now = new Date();
  const dateStr = formatDate(now, 'yyyyMMddHHmm');
  const fileName = `${dateStr}_${name}`;
  ele.setAttribute('download', fileName);
  document.body.appendChild(ele);
  ele.click();
  document.body.removeChild(ele);
  window.URL.revokeObjectURL(url);
};
