<template>
  <div
    class="wapper"
    style="display: flex; flex-direction: column; height: 100vh; overflow: hidden"
  >
    <div>
      <base-tips
        title="灰度发布管理"
        class="mb-10"
      >
        <div slot="title">
          <span>灰度发布管理</span>
        </div>
      </base-tips>
    </div>
    <div
      class="gray-release-manage"
      style="display: flex; flex: 1; min-height: 0"
    >
      <!-- 左侧组织树区域 -->
      <el-aside
        :width="asideWidth + 'px'"
        class="aside"
        style="overflow-y: auto; overflow-x: auto; flex-shrink: 0; padding-bottom: 20px"
        ref="aside"
      >
        <el-tree
          :key="treeKey"
          ref="departmentTree"
          :data="orgTreeData"
          :props="treeProps"
          @node-click="handleNodeClick"
          :highlight-current="true"
          :default-expanded-keys="defaultExpandedKeys"
          nodeKey="departmentId"
        />
      </el-aside>
      <!-- 拖拽手柄 -->
      <div
        class="resize-handle"
        @mousedown="startDrag"
        style="flex-shrink: 0; align-self: stretch"
      ></div>

      <!-- 右侧内容区域 -->

      <el-main
        class="main"
        style="flex: 1; display: flex; flex-direction: column; min-height: 0; padding: 0"
        ref="main"
      >
        <card
          style="
            flex: 1;
            display: flex;
            flex-direction: column;
            min-height: 0;
            margin: 0;
            border-radius: 0;
            border: none;
            box-shadow: none;
          "
        >
          <!-- 查询条件区域 -->
          <el-form
            :inline="true"
            :model="searchForm"
            class="search-form"
          >
            <el-form-item label="用户姓名">
              <el-select
                v-model="searchForm.userCode"
                filterable
                remote
                reserveKeyword
                placeholder="请输入用户姓名或工号"
                :remote-method="remoteSearchUser"
                :loading="userSearchLoading"
              >
                <el-option
                  v-for="item in userOptions"
                  :key="item.userCode"
                  :label="`${item.userName}`"
                  :value="item.userCode"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                @click="handleQuery"
                >查询</el-button
              >
            </el-form-item>
            <el-form-item>
              <el-button @click="handleReset">重置</el-button>
            </el-form-item>

            <el-form-item>
              <el-button
                type="warning"
                @click="handleOneKeyPublish"
                >一键发布</el-button
              >
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                @click="handleVersionPublish"
                >版本发布</el-button
              >
            </el-form-item>
          </el-form>
          <!-- 数据表格区域 -->
          <div style="flex: 1; display: flex; flex-direction: column; min-height: 0">
            <!-- 表格区域 -->
            <div style="flex: 1; overflow: auto; min-height: 0">
              <el-table
                :data="tableData"
                border
                v-loading="tableLoading"
                style="width: 100%"
                height="100%"
              >
                <el-table-column
                  prop="userCode"
                  label="登录名"
                />
                <el-table-column
                  prop="userName"
                  label="姓名"
                />
                <el-table-column
                  prop="mobile"
                  label="手机号"
                />
                <el-table-column
                  prop="versionNo"
                  label="版本号"
                />
                <el-table-column
                  prop="usagePeriodTime"
                  label="更新时间"
                />
                <el-table-column label="操作">
                  <template slot-scope="scope">
                    <el-button
                      type="text"
                      @click="handleHistory(scope.row)"
                      >历史记录</el-button
                    >
                    <el-button
                      type="text"
                      @click="handleVersionSetting(scope.row)"
                      >版本设置</el-button
                    >
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <!-- 分页区域 -->
            <div style="margin-top: 10px; padding: 10px; border-top: 1px solid #e6e6e6; background: #fff">
              <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="pageInfo.page"
                :page-sizes="[10, 20, 30, 40]"
                :page-size="pageInfo.size"
                layout="total, sizes, prev, pager, next, jumper"
                :total="pageInfo.total"
                style="text-align: right"
              />
            </div>
          </div>
        </card>
      </el-main>
    </div>
    <!-- 一键发布弹窗 -->
    <el-dialog
      title="一键发布"
      :visible.sync="oneKeyPublishDialogVisible"
      width="400px"
    >
      <el-form
        :model="oneKeyPublishForm"
        label-width="80px"
      >
        <el-form-item label="用户端">
          <el-select
            v-model="oneKeyPublishForm.client"
            placeholder="请选择"
          >
            <el-option
              v-for="item in clientOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="选择版本">
          <el-select
            v-model="oneKeyPublishForm.version"
            placeholder="请选择"
          >
            <el-option
              v-for="item in versionOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button @click="oneKeyPublishDialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="confirmOneKeyPublish"
          >确认</el-button
        >
      </div>
    </el-dialog>
    <!-- 版本发布弹窗 -->
    <el-dialog
      title="版本发布"
      :visible.sync="versionPublishDialogVisible"
      width="500px"
      @close="handleVersionPublishDialogClose"
    >
      <div class="org-user-selection mb-20">
        <div class="selection-content">
          选择组织及用户：

          <!-- <el-cascader

            v-model="selectedOrgPaths"
            :options="orgCascaderData"
            :props="cascaderProps"
            clearable
            style="width: 200px"
          /> -->
          <org-user-cascader
            :key="cascaderKey"
            v-if="!isFromVersionSetting"
            v-model="form.userIds"
            :sync-time="userSyncTime"
            @change="handleUserChange"
          />

          <div
            v-else
            class="fixed-org-path"
          >
            {{ currentOrgPath }}
          </div>
          <el-tooltip
            content="请先展开到末级组织和人之后再勾选"
            placement="top"
          >
            <i
              v-if="!isFromVersionSetting"
              class="el-icon-question"
              style="color: #409eff; margin-left: 5px"
            ></i>
          </el-tooltip>
        </div>
      </div>
      <el-form
        :model="versionPublishForm"
        label-width="110px"
      >
        <el-form-item label="用户端：">
          <el-select
            v-model="versionPublishForm.client"
            placeholder="请选择"
            filterable
            allowCreate
            defaultFirstOption
            remote
          >
            <el-option
              v-for="item in clientOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="版本号：">
          <el-select
            v-model="versionPublishForm.version"
            placeholder="请选择"
          >
            <el-option
              v-for="item in versionOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button @click="versionPublishDialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="confirmVersionPublish"
          >提交</el-button
        >
      </div>
    </el-dialog>

    <!-- 历史记录弹窗 -->
    <el-dialog
      title="历史记录"
      :visible.sync="historyDialogVisible"
      width="800px"
    >
      <el-table
        :data="historyData"
        border
        v-loading="historyLoading"
        style="width: 100%"
      >
        <el-table-column
          prop="versionNo"
          label="版本号"
        />
        <el-table-column
          prop="versionDate"
          label="版本统一发布时间"
        />
        <el-table-column
          prop="usagePeriodTime"
          label="用户更新时间"
        />
      </el-table>
      <el-pagination
        @size-change="handleHistorySizeChange"
        @current-change="handleHistoryCurrentChange"
        :current-page="historyPageInfo.page"
        :page-sizes="[10, 20, 30, 40]"
        :page-size="historyPageInfo.size"
        layout="total"
        :total="historyPageInfo.total"
        style="margin-top: 20px; text-align: right"
      />
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button @click="historyDialogVisible = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import card from '@/common/components/card/card.vue';
import {
  getDepartmentTree,
  getQaversionusage,
  getQaversionusageList,
  VersionList,
  addQaversionusage,
  listTop20UserName, // 替换 searchUser 为 listTop20UserName
} from '@/common/api/data';
// 导入组件
import OrgUserCascader from '@/common/components/OrgUserCascader.vue';
export default {
  name: 'GrayReleaseManage',
  components: { card, OrgUserCascader },
  data() {
    return {
      // ...其他数据
      form: {
        userIds: [], // 用户ID数组
      },
      asideWidth: 250, // 侧边栏初始宽度
      isDragging: false, // 是否正在拖拽
      startX: 0, // 拖拽开始时的鼠标X坐标
      startWidth: 0, // 拖拽开始时的侧边栏宽度
      userSyncTime: [], // 用户同步时间范围
      // 组织树数据
      orgTreeData: [],
      treeProps: {
        label: 'departmentName',
        children: 'children',
      },
      // 添加表格loading状态
      tableLoading: false,
      // 默认展开的节点ID
      defaultExpandedKeys: [],
      // 查询表单
      searchForm: {
        userCode: '', // 将 username 改为 userCode
      },
      // 表格数据
      tableData: [],
      // 分页信息
      pageInfo: {
        page: 1, // 当前页，与截图一致
        size: 10,
        total: 0, // 示例总数，实际根据接口返回
      },
      // 一键发布弹窗相关
      oneKeyPublishDialogVisible: false,
      oneKeyPublishForm: {
        client: '1',
        version: '',
      },
      // 版本发布弹窗相关
      versionPublishDialogVisible: false,
      versionPublishForm: {
        client: '1',
        version: '',
      },
      // 是否从版本设置进入弹窗
      isFromVersionSetting: false,
      // 当前用户的组织路径
      currentOrgPath: '',
      currentUserCode: '', // 添加当前用户编码变量
      // 新增：级联选择器数据（转换自orgTreeData）
      orgCascaderData: [],
      // 新增：选中的组织路径
      selectedOrgPaths: [],
      // 新增：级联选择器属性配置
      cascaderProps: {
        multiple: true,
        label: 'departmentName',
        value: 'departmentId',
        children: 'children',
      },
      // 用户端选项
      clientOptions: [{ value: '1', label: '用户端' }],
      // 版本选项
      versionOptions: [],
      // 新增：历史记录loading状态
      historyLoading: false,
      // 新增：历史记录弹窗相关
      historyDialogVisible: false,
      historyData: [], // 清空模拟数据
      historyPageInfo: {
        page: 1, // 重置为第一页
        size: 10,
        total: 0,
      },
      // 用户搜索相关属性
      userOptions: [], // 存储搜索到的用户选项
      userSearchLoading: false, // 搜索加载状态
      treeKey: 0,
      cascaderKey: 0,
    };
  },
  created() {
    this.loadDepartmentTree();
    this.loadVersionList(); // 添加这行代码
  },
  methods: {
    // 开始拖拽
    startDrag(e) {
      this.isDragging = true;
      this.startX = e.clientX;
      this.startWidth = this.asideWidth;
      document.addEventListener('mousemove', this.onDrag);
      document.addEventListener('mouseup', this.stopDrag);
      e.preventDefault(); // 防止选择文本
    },

    // 拖拽中
    onDrag(e) {
      if (!this.isDragging) return;

      // 计算宽度变化
      const widthChange = e.clientX - this.startX;
      // 计算新宽度（限制最小宽度为150px，最大宽度为500px）
      const newWidth = Math.max(150, Math.min(500, this.startWidth + widthChange));
      this.asideWidth = newWidth;
    },

    // 停止拖拽
    stopDrag() {
      this.isDragging = false;
      document.removeEventListener('mousemove', this.onDrag);
      document.removeEventListener('mouseup', this.stopDrag);
    },

    // 加载版本列表
    async loadVersionList() {
      try {
        // 调用VersionList接口，不需要分页，所以传递空对象
        const res = await VersionList({});

        if (res.code === 200) {
          // 将版本数据转换为下拉框需要的格式
          this.versionOptions = res.data.map((item) => ({
            value: item.id,
            label: item.versionNo,
          }));
        } else {
          this.$message.error('获取版本列表失败');
          console.error('获取版本列表失败:', res);
        }
      } catch (error) {
        this.$message.error('获取版本列表发生异常');
        console.error('获取版本列表异常:', error);
      }
    },
    // 处理用户选择变化
    handleUserChange(value) {
      console.log('选中的用户/部门:', value);
      // 更新form.userIds
      this.form.userIds = value;
    },
    // 设置用户同步时间
    setUserSyncTime(date) {
      this.userSyncTime = date;
    },
    async loadDepartmentTree() {
      try {
        const res = await getDepartmentTree();
        if (res.code === 200) {
          // 使用接口返回的部门树数据
          this.orgTreeData = res.data;
          // 更新级联选择器数据
          this.orgCascaderData = this.transformTreeToCascader(this.orgTreeData);
          // 设置默认展开第一个节点
          if (this.orgTreeData && this.orgTreeData.length > 0) {
            // 使用Vue.set确保响应式更新
            this.$set(this, 'defaultExpandedKeys', [this.orgTreeData[0].departmentId]);
            // 强制刷新树组件
            this.$nextTick(() => {
              const tree = this.$refs.departmentTree;
              if (tree && this.defaultExpandedKeys.length > 0) {
                tree.setCurrentKey(this.defaultExpandedKeys[0]);
                // 重置页码为1
                this.pageInfo.page = 1;
                // 新增：设置默认节点后立即加载数据
                this.getTableDataByDepartmentId(this.defaultExpandedKeys[0]);
              }
            });
          }
        } else {
          this.$message.error('获取部门树失败');
          console.error('获取部门树失败:', res);
        }
      } catch (error) {
        this.$message.error('获取部门树发生异常');
        console.error('获取部门树异常:', error);
      }
    },
    // 组织树节点点击事件
    handleNodeClick(data) {
      console.log('点击组织节点：', data);
      // 更新默认展开的节点
      this.$set(this, 'defaultExpandedKeys', [data.departmentId]);
      // 重置页码为1
      this.pageInfo.page = 1;
      // 调用接口获取表格数据
      this.getTableDataByDepartmentId(data.departmentId);
    },
    // 查询按钮点击
    handleQuery() {
      console.log('查询条件：', this.searchForm);
      // 重置页码为1
      this.pageInfo.page = 1;
      if (this.defaultExpandedKeys.length > 0) {
        this.getTableDataByDepartmentId(this.defaultExpandedKeys[0]);
      } else {
        this.$message.warning('请先选择部门');
      }
    },
    // 重置按钮点击
    handleReset() {
      // 重置查询表单
      this.searchForm = {
        userCode: '', // 将 username 改为 userCode
      };
      // 重置菜单状态到初始展开状态
      if (this.orgTreeData && this.orgTreeData.length > 0) {
        // 设置默认展开键，只包含第一个节点
        this.defaultExpandedKeys = [this.orgTreeData[0].departmentId];
        // 强制刷新树组件
        this.treeKey = this.treeKey + 1;
        this.$nextTick(() => {
          const tree = this.$refs.departmentTree;
          if (tree) {
            // 设置选中节点
            tree.setCurrentKey(this.defaultExpandedKeys[0]);
            // 直接更新展开节点，利用 Vue 的响应式特性
            // 不需要调用 toggleExpand 或 setExpandedKeys 方法
          }
        });
      }
      // 重置后更新请求
      if (this.defaultExpandedKeys.length > 0) {
        this.getTableDataByDepartmentId(this.defaultExpandedKeys[0]);
      } else {
        this.$message.warning('请先选择部门');
      }
    },

    // 一键发布按钮点击
    handleOneKeyPublish() {
      console.log('执行一键发布操作');
      this.oneKeyPublishDialogVisible = true;
      // 重置表单
      this.oneKeyPublishForm = {
        client: '1',
        version: '',
      };
    },
    // 确认一键发布
    async confirmOneKeyPublish() {
      console.log('确认一键发布：', this.oneKeyPublishForm);

      // 1. 表单验证
      if (!this.oneKeyPublishForm.client) {
        this.$message.error('请选择用户端');
        return;
      }

      if (!this.oneKeyPublishForm.version) {
        this.$message.error('请选择版本号');
        return;
      }

      // 2. 显示二次确认弹窗
      try {
        await this.$confirm('确定要执行一键全量发布吗？此操作将影响所有用户！', '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        });

        // 3. 用户确认后，继续执行发布操作
        const requestData = {
          versionId: this.oneKeyPublishForm.version,
          fullRelease: 1, // 1表示一键全量发布
          client: this.oneKeyPublishForm.client,
        };

        const res = await addQaversionusage(requestData);

        if (res.code === 200) {
          this.oneKeyPublishDialogVisible = false;
          this.$message.success('一键发布成功');
          // 刷新表格数据
          if (this.defaultExpandedKeys.length > 0) {
            this.getTableDataByDepartmentId(this.defaultExpandedKeys[0]);
          }
        } else {
          this.$message.error('一键发布失败: ' + (res.data.msg || '未知错误'));
          console.error('一键发布失败:', res);
        }
      } catch (error) {
        // 捕获用户取消操作的异常
        if (error === 'cancel') {
          this.$message.info('已取消一键发布操作');
          return;
        }

        this.$message.error('一键发布发生异常');
        console.error('一键发布异常:', error);
      }
    },

    // 新增：组织树节点点击事件（弹窗中）
    handleOrgTreeClick(data) {
      console.log('弹窗中选择组织节点：', data);
      // 这里可以根据选中的节点更新选中的用户数量
      // this.selectedUserCount = ...;
    },
    // 确认版本发布
    async confirmVersionPublish() {
      // 1. 表单验证
      // 验证是否选择用户
      if (this.isFromVersionSetting) {
        // 从版本设置进入，需要确保有当前行数据
        if (!this.currentUserCode) {
          this.$message.error('请选择用户');
          return;
        }
      } else {
        // 直接发布，需要确保选择了用户
        if (!this.form.userIds || this.form.userIds.length === 0) {
          this.$message.error('请至少选择一个用户');
          return;
        }
      }

      // 验证用户端是否选择
      if (!this.versionPublishForm.client) {
        this.$message.error('请选择用户端');
        return;
      }

      // 验证版本号是否选择
      if (!this.versionPublishForm.version) {
        this.$message.error('请选择版本号');
        return;
      }

      try {
        // 2. 准备请求参数
        const requestData = {
          versionId: this.versionPublishForm.version, // 版本号
          userCode: this.isFromVersionSetting ? this.currentUserCode : this.form.userIds.join('|'), // 用户编码，多个用逗号分隔
        };

        // 3. 调用接口
        const res = await addQaversionusage(requestData);

        if (res.code === 200) {
          // 4. 成功处理
          this.versionPublishDialogVisible = false;
          this.$message.success('版本发布成功');
          // 刷新表格数据
          if (this.defaultExpandedKeys.length > 0) {
            this.getTableDataByDepartmentId(this.defaultExpandedKeys[0]);
          }
        } else {
          this.$message.error('版本发布失败: ' + (res.data.msg || '未知错误'));
          console.error('版本发布失败:', res);
        }
      } catch (error) {
        this.$message.error('版本发布发生异常');
        console.error('版本发布异常:', error);
      }
    },
    // 历史记录操作
    handleHistory(row) {
      console.log('查看历史记录：', row);
      this.historyDialogVisible = true;
      // 调用接口获取历史记录数据，传递userCode参数
      this.getHistoryData(row.userCode);
    },
    // 新增：历史记录分页 - 每页条数改变
    handleHistorySizeChange(val) {
      this.historyPageInfo.size = val;
      this.getHistoryData();
    },
    // 新增：历史记录分页 - 当前页改变
    handleHistoryCurrentChange(val) {
      this.historyPageInfo.page = val;
      this.getHistoryData();
    },
    // 新增：获取历史记录数据
    async getHistoryData(userCode) {
      try {
        // 显示loading
        this.historyLoading = true;

        // 调用接口，传递userCode参数
        const res = await getQaversionusageList(userCode);

        if (res.code === 200) {
          // 更新历史记录数据
          this.historyData = res.data || [];
          // 更新总条数（虽然不需要分页，但为了保持分页组件显示正确）
          this.historyPageInfo.total = this.historyData.length;
        } else {
          this.$message.error('获取历史记录失败');
          console.error('获取历史记录失败:', res);
        }
      } catch (error) {
        this.$message.error('获取历史记录发生异常');
        console.error('获取历史记录异常:', error);
      } finally {
        // 隐藏loading
        this.historyLoading = false;
      }
    },

    // 分页 - 当前页改变
    handleCurrentChange(val) {
      this.pageInfo.page = val;
      this.getTableData();
    },
    // 根据部门ID获取表格数据
    async getTableDataByDepartmentId(departmentId) {
      try {
        // 显示loading
        this.tableLoading = true;
        // 准备请求参数
        const requestParams = {
          page: {
            pageNum: this.pageInfo.page,
            pageSize: this.pageInfo.size,
          },
          filter: {},
        };

        // 只有当用户名不为空时才添加userName参数
        // 只有当用户编码不为空时才添加userCode参数
        if (this.searchForm.userCode) {
          requestParams.filter.userCode = this.searchForm.userCode;
        }

        // 调用接口
        const res = await getQaversionusage(departmentId, requestParams);
        if (res.code === 200) {
          // 更新表格数据
          this.tableData = res.data.dataList || [];
          // 更新分页信息
          this.pageInfo.total = res.data.totalCount || 0;
        } else {
          this.$message.error('获取数据失败');
          console.error('获取数据失败:', res);
        }
      } catch (error) {
        this.$message.error('获取数据发生异常');
        console.error('获取数据异常:', error);
      } finally {
        // 隐藏loading
        this.tableLoading = false;
      }
    },
    // 模拟获取表格数据方法（实际需对接接口）
    getTableData() {
      // 结合 searchForm、pageInfo 等参数，调用后端接口获取数据并更新 tableData 和 pageInfo.total
      console.log('发起请求获取表格数据，参数：', this.searchForm, this.pageInfo);
      // 如果有选中的部门，根据部门ID获取数据
      if (this.defaultExpandedKeys.length > 0) {
        this.getTableDataByDepartmentId(this.defaultExpandedKeys[0]);
      }
    },
    // 新增：转换树形数据为级联选择器数据（添加id）
    transformTreeToCascader(treeData, parentId = 0) {
      return treeData.map((item, index) => {
        const id = parentId * 100 + (index + 1);
        const newItem = {
          ...item,
          id,
        };
        if (item.children && item.children.length > 0) {
          newItem.children = this.transformTreeToCascader(item.children, id);
        }
        return newItem;
      });
    },
    // 版本设置操作
    handleVersionSetting(row) {
      console.log('进行版本设置：', row);
      this.versionPublishDialogVisible = true;
      // 重置表单
      this.versionPublishForm = {
        client: '1', // 设置用户端默认值为"1"
        version: '',
      };
      // 标记为从版本设置进入
      this.isFromVersionSetting = true;
      // 设置当前用户的组织路径
      // 查找组织路径
      const orgPath = this.findDepartmentPath(this.orgTreeData, row.mainDepartment);
      this.currentOrgPath = orgPath ? `${orgPath}-${row.userName}` : `未知组织/${row.userName}`;
      // 存储当前用户的userCode
      this.currentUserCode = row.userCode;
    },

    // 新增：根据部门ID查找组织路径
    findDepartmentPath(treeData, departmentId) {
      if (!departmentId) return '';

      // 递归查找部门
      function searchDepartment(tree, id, path = []) {
        for (const node of tree) {
          if (node.departmentId === id) {
            return [...path, node.departmentName];
          }
          if (node.children && node.children.length > 0) {
            const result = searchDepartment(node.children, id, [...path, node.departmentName]);
            if (result) {
              return result;
            }
          }
        }
        return null;
      }

      const pathArray = searchDepartment(treeData, departmentId);
      return pathArray ? pathArray.join('/') : '';
    },
    // 版本发布按钮点击
    handleVersionPublish() {
      console.log('执行版本发布操作');
      this.versionPublishDialogVisible = true;
      // 重置表单
      this.versionPublishForm = {
        client: '1',
        version: '',
      };
      // 标记为非从版本设置进入
      this.isFromVersionSetting = false;
      // 重置选中的组织路径
      this.selectedOrgPaths = [];
    },

    // 分页 - 每页条数改变
    handleSizeChange(val) {
      this.pageInfo.size = val;
      this.getTableData(); // 假设有获取表格数据的方法，需根据实际封装
    },
    /**
     * 远程搜索用户
     * @param {string} query 搜索关键词
     */
    async remoteSearchUser(query) {
      if (!query) {
        this.userOptions = [];
        return;
      }

      this.userSearchLoading = true;
      try {
        // 使用 listTop20UserName 替代 searchUser
        const res = await listTop20UserName(query);

        // 初始化用户选项数组
        let options = [];

        if (res.code === 200 && res.data) {
          // 格式化搜索结果，过滤掉可能的null值
          options = res.data
            .filter((item) => item && item.userCode !== undefined)
            .map((item) => ({
              value: item.userCode,
              userName: item.userName || '',
              userCode: item.userCode,
            }));
        } else {
          this.$message.error('搜索用户失败');
          console.error('搜索用户失败:', res);
        }

        // 检查是否存在与query完全匹配的用户名或用户编码
        const hasExactMatch = options.some(
          (item) =>
            (item.userName && item.userName.toLowerCase() === String(query).toLowerCase()) ||
            (item.userCode && String(item.userCode).toLowerCase() === String(query).toLowerCase()),
        );

        // 只有当没有完全匹配时，才将用户输入添加到选项列表开头
        if (!hasExactMatch) {
          options.unshift({
            value: query, // 用户输入的内容作为value
            userName: `${query}`, // 显示为用户输入的内容
            userCode: query, // 用户输入的内容作为userCode
          });
        }

        // 更新用户选项
        this.userOptions = options;
      } catch (error) {
        this.$message.error('搜索用户发生异常');
        console.error('搜索用户异常:', error);
        // 发生异常时，仍将用户输入的内容添加到选项列表
        this.userOptions = [
          {
            value: query,
            userName: `${query}`,
            userCode: query,
          },
        ];
      } finally {
        this.userSearchLoading = false;
      }
    },
    /**
     * 处理版本发布对话框关闭
     */
    handleVersionPublishDialogClose() {
      console.log('关闭对话框，清空选中项');
      // 使用Vue.set确保响应式更新被正确触发
      this.form.userIds.splice(0, this.form.userIds.length);
      this.cascaderKey = this.cascaderKey + 1;
      // 重置其他相关状态
      this.isFromVersionSetting = false;
      this.currentOrgPath = '';
      this.currentUserCode = '';
    },
  },
  mounted() {
    this.getTableData();
  },
};
</script>

<style scoped>
.aside {
  background-color: #f5f7fa;
  border-right: 1px solid #e6e6e6;
  overflow-y: auto;
}

/* 拖拽手柄样式 */
.resize-handle {
  width: 8px;
  cursor: col-resize;
  background-color: #e4e7ed;
  position: relative;
  transition: background-color 0.2s;
  &:hover {
    background-color: #c0c4cc;
  }
  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 12px;
    height: 24px;
    background-image: url('data:image/svg+xml;utf8,<svg t="1755759805275" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5662" width="32" height="32"><path d="M888.32 140.8H135.68c-4.224 0-7.68 4.608-7.68 10.24v81.92c0 5.632 3.456 10.24 7.68 10.24h752.64c4.224 0 7.68-4.608 7.68-10.24V151.04c0-5.632-3.456-10.24-7.68-10.24z m0 640H135.68c-4.224 0-7.68 4.608-7.68 10.24v81.92c0 5.632 3.456 10.24 7.68 10.24h752.64c4.224 0 7.68-4.608 7.68-10.24v-81.92c0-5.632-3.456-10.24-7.68-10.24z m0-320H135.68c-4.224 0-7.68 4.608-7.68 10.24v81.92c0 5.632 3.456 10.24 7.68 10.24h752.64c4.224 0 7.68-4.608 7.68-10.24V471.04c0-5.632-3.456-10.24-7.68-10.24z" fill-opacity=".65" p-id="5663"></path></svg>');
    background-repeat: no-repeat;
    background-size: contain;
  }
}
.main {
  flex: 1;
  padding: 0px 20px 20px 20px;
}
.search-form {
  margin-bottom: 20px;
}

.mb-10 {
  margin-bottom: 10px;
}

/* 版本发布弹窗样式 */
.org-user-selection {
  margin-bottom: 20px;
}
.selection-title {
  font-weight: bold;
  margin-bottom: 10px;
}

.user-count {
  background-color: #f5f5f5;
  padding: 5px 10px;
  border-radius: 10px;
  font-size: 12px;
}

.mb-20 {
  margin-bottom: 20px;
}
.el-dialog__header {
  background-color: #f5f7fa;
  border-bottom: 1px solid #e6e6e6;
}
.el-dialog__title {
  font-size: 16px;
  font-weight: bold;
}
.el-dialog__footer {
  border-top: 1px solid #e6e6e6;
  padding: 15px 20px;
  text-align: right;
}
</style>

<style scoped>
.gray-release-manage {
  display: flex;
  width: 100%;
}
.aside {
  background-color: #f5f7fa;
  border-right: 1px solid #e6e6e6;
}
.main {
  flex: 1;
  padding: 0px 20px 20px 20px;
}
.search-form {
  margin-bottom: 20px;
}

.mb-10 {
  margin-bottom: 10px;
}

/* 版本发布弹窗样式 */
.org-user-selection {
  margin-bottom: 20px;
}
.selection-title {
  font-weight: bold;
  margin-bottom: 10px;
}
.selection-content {
  display: flex;
  align-items: center;
}
.user-count {
  background-color: #f5f5f5;
  padding: 5px 10px;
  border-radius: 10px;
  font-size: 12px;
}
.fixed-org-path {
  padding: 5px 10px;
  background-color: #f9f9f9;
  border-radius: 4px;
  width: 75%;
  /* white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis; */
}
.mb-20 {
  margin-bottom: 20px;
}
</style>
