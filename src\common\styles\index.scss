@import "./element-ui.scss";
@import "./sidebar.scss";

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  list-style: none;
}

html {
  height: 100%;
}

body {
  width: 100%;
  height: 100%;
  font-family:
    <PERSON><PERSON><PERSON> SC,
    Microsoft YaHei,
    Arial,
    sans-serif;
  background: #f1f2f6;
  overflow-x: hidden;
}

*:before,
*:after {
  box-sizing: inherit;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}


::-webkit-scrollbar-track {
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(144, 147, 153, 0.3);
  border-radius: 10px;
  min-height: 20px;
  background-clip: content-box;
}

::-webkit-scrollbar {
  width: 16px;
  height: 16px;
}

::-webkit-scrollbar-track,
::-webkit-scrollbar-thumb {
  border-radius: 999px;
  border: 5px solid transparent;
}

::-webkit-scrollbar-corner {
  background: transparent;
}

html::-webkit-scrollbar,
body::-webkit-scrollbar,
#app::-webkit-scrollbar {
  width: 0;
}

.dialog_content {
  height: 400px;
  padding: 10px;
  overflow-y: auto;
}

::v-deep .el-dialog__headerbtn {
  top: 20px;
}

fieldset {
  border: 1px solid #e1e5ec;
  padding: 5px 10px;
  margin-bottom: 10px;
  border-radius: 2px;
}

legend {
  font-weight: bold;
  font-size: 14px;
  color: #333;
  padding: 0 10px;
  background: #fff;
}

.box-card {
  margin: 10px 0;
}

.el-form-item__label {
  padding: 0;
  padding-right: 10px;
}
.viewVox {
  padding: 10px;
  background: #fff;

  .el-form {
    border: 1px solid #ddd;
    background: #fff;
    display: table;
    border-bottom: none;
    border-right: none;
  }

  .el-input-number__decrease,
  .el-input-number__increase {
    display: none;
  }

  .el-textarea {
    margin-bottom: 10px;
  }

  .static-content-item {
    border-right: 1px solid #ddd;
  }

  .el-select {
    width: 100%;
  }

  .el-form-item__error {
    top: 61%;
    left: 24px;
    z-index: 2;
  }

  .el-form-item {
    margin-bottom: 0;
    border-bottom: 1px solid #ddd;

    .el-form-item__content {
      border-right: 1px solid #ddd;
      border-left: 1px solid #ddd;
      padding: 5px 10px 0;

      .el-input__inner,
      .el-textarea__inner {
        border: none;
      }
    }

    label {
      padding: 5px 0 5px 10px;
    }
  }
}
.el-select {
  width: 100%;
}
.el-tree-node__content {
  height: 40px;
}
.svg-icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
.sn-dialog__footer {
  .el-button + .el-button {
    margin-left: 16px !important;
  }
}
.sn-form__menu--center {
  .el-button {
    margin: 0;
  }
  .el-button + .el-button {
    margin-left: 16px !important;
  }
}
.el-submenu .el-submenu__icon-arrow {
  font-size: 14px;
}
.el-drawer__header {
  border-bottom: 1px solid #dcdfe6 !important;
}

.main_contation {
  width: 100%;
}
#qiankunContainer>div{
  height: 100%;
}
.flex1{
  flex: 1;
}

.flex{
   display:flex;
   align-items: center;
}
.flex-column{
  flex-direction: column;
}
.flex-center{
  justify-content: center;
}
.flex-between{
  justify-content: space-between;
}
.flex-around{
  justify-content: space-around;
}
.flex-end{
  justify-content: flex-end;
}