import './public-path';
import '@/common/components';
import Vue from 'vue';
import App from '@/App.vue';
import LayoutView from '@/common/layoutView/LayoutIndex';
import router, { constantRoutes } from '@/router';
import store from '@/store';
import plugins from '@common/plugins';
import { loadView, readLocalSrcFiles } from '@/common/utils';
import { startQiankun, actionsQiankun } from '@/common/utils/qiankun';
import {
  useStorePlugin,
  getToken,
  setAppName,
  parseTime,
  hasPermi,
  selectDictLabel,
  restPage,
  download,
  changeElTheme,
} from 'sn-base-utils';
import SnBaseLayout from 'sn-base-layout';
import ElementUI from 'sn-element';
import SnpitUI, { userPermis } from 'snpit-ui';
import '@common/styles/element-variables.scss';
import '@common/styles/index.scss';
import config, { isCloud } from '@/config.js';
// 自定义角色筛选
import FitterRole from '@common/components/fitterRole';
import 'snpit-ui/lib/snpit-ui.css';
// 字典相关

import '@common/assets/icons';
import '@/permission';
const urlParams = new URLSearchParams(window.location.search);
// 全局覆盖主题色
// 主题设计器导出js的时候使用
Promise.resolve().then(() => {
  require('@common/utils/SetGlobal.js');
});
Vue.use(ElementUI);
Vue.use(plugins);
Vue.use(SnpitUI, {
  prefix: isCloud
    ? `${process.env.VUE_APP_BASE_API}/${process.env.VUE_APP_APPLATION_NAME}`
    : process.env.VUE_APP_BASE_API,
  getToken: getToken,
  calcHeight: urlParams.get('isFullscreen') ? 0 : 108,
});
Vue.use(userPermis, {
  store: store,
});
Vue.use(useStorePlugin, {
  config, // 可选, 项目基础配置
  noPermission: () => store.getters.superAdmin || false, // 可选, 项目基础配置
});
Vue.use(
  SnBaseLayout,
  {
    routerStore: {
      router,
      constantRoutes,
      loadView,
    },
    qiankunStore: {
      startQiankun,
      actionsQiankun,
    },
    envStore: {
      NODE_ENV: process.env.NODE_ENV,
      VUE_APP_APPLATION_NAME: process.env.VUE_APP_APPLATION_NAME,
      VUE_APP_BASE_API: process.env.VUE_APP_BASE_API,
    },
    viewStore: {
      LayoutView,
    },
  },
  readLocalSrcFiles,
);
// 前台的数据表格
import commonFormItem from 'common_form_item';
import searchTable from './common/components/search-table/index';
import baseCard from './common/components/card/card.vue';
import baseTips from './common/components/tips/tips.vue';
Vue.use(commonFormItem);
Vue.use(searchTable);
Vue.component('baseCard', baseCard);
Vue.component('baseTips', baseTips);

// 全局组件挂载
Vue.component('FitterRole', FitterRole);

Vue.config.productionTip = false;
// 项目中所有拥有 size 属性的组件的默认尺寸均为 'small'，弹框的初始 z-index 为 3000
Vue.prototype.$ELEMENT = { zIndex: 3000 };
// Vue.prototype.getDicts = DictApi.getDicts;
Vue.prototype.hasPermi = hasPermi;
Vue.prototype.selectDictLabel = selectDictLabel;
Vue.prototype.download = download;
Vue.prototype.parseTime = parseTime;
Vue.prototype.restPage = restPage;

// eslint-disable-next-line no-unused-vars
let routers = null;
let instance = null;
let addRouter = false;
function render(props = {}) {
  const { container } = props;
  routers = router;
  instance = new Vue({
    router,
    store,
    render: (h) => h(App),
  }).$mount(container ? container.querySelector('#app-container') : '#app-container');
}

// 独立运行时
if (!window.__POWERED_BY_QIANKUN__) {
  if (isCloud) {
    setAppName(process.env.VUE_APP_APPLATION_NAME);
    store.dispatch('app/changeIsFullscreen', true);
  }
  render();
} else {
  store.dispatch('app/changeIsFullscreen', false);
}

export async function bootstrap() {
  // console.log("[vue] vue app bootstraped");
}
export async function mount(props) {
  // console.log("[vue] props from main framework", props.remoteRouters);
  if (props.remoteRouters && !addRouter) {
    addRouter = true;
    store.dispatch('GenerateRoutes', { routerArr: props.remoteRouters }).then((accessRoutes) => {
      for (const x of accessRoutes) {
        if (x.isFrame == 1) {
          router.addRoute(x);
        }
      }
    });
  }
  props.onGlobalStateChange((state) => {
    // state: 变更后的状态; prev 变更前的状态
    if (state.theme && state.theme != store.getters.theme) {
      changeElTheme(state.theme);
    }
  });
  render(props);
}
export async function unmount() {
  instance.$destroy();
  instance.$el.innerHTML = '';
  instance = null;
  routers = null;
}
