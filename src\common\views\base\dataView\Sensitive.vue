<template>
  <div>
    <base-tips
      title="敏感词词库"
      class="mb-10"
    ></base-tips>

    <!-- 操作按钮 -->
    <el-button
      @click="openAddDialog"
      style="margin-bottom: 10px"
      >新增</el-button
    >
    <!-- <el-button
      @click="batchImport"
      style="margin-bottom: 10px"
      >批量导入</el-button
    > -->

    <!-- 表格区域 -->
    <search-table
      v-loading="loading"
      :form="form"
      :fields="fields"
      :columns="columns"
      :data="sensitiveList"
      :query="queryTable"
      :total="total"
      :auto-height="false"
      height="500px"
      :row-height="40"
    >
    </search-table>

    <!-- 新增敏感词弹窗 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="commonDialogVisible"
      width="500px"
      append-to-body
      @open="initCommonDialog"
      @close="pushDialogClose"
    >
      <el-form
        :model="currentForm"
        :rules="currentRules"
        ref="commonForm"
        label-width="120px"
      >
        <el-form-item
          label="敏感词："
          prop="sensitiveWord"
        >
          <el-input v-model="currentForm.sensitiveWord"></el-input>
        </el-form-item>

        <el-form-item
          label="匹配方式："
          prop="matchMode"
        >
          <el-select
            v-model="currentForm.matchMode"
            placeholder="请选择"
          >
            <el-option
              v-for="item in matchModes"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="敏感词类型："
          prop="wordType"
        >
          <el-input v-model="currentForm.wordType"></el-input>
        </el-form-item>
      </el-form>
      <span
        slot="footer"
        class="dialog-footer"
      >
        <el-button @click="commonDialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="submitCommonForm"
          >提交</el-button
        >
      </span>
    </el-dialog>
    <!-- 拦截详情弹窗 -->
    <el-dialog
      :title="'拦截详情'"
      :visible.sync="interceptDialogVisible"
      width="800px"
      append-to-body
    >
      <!-- <h3>提问详情</h3> -->
      <el-table :data="interceptDetails">
        <el-table-column
          prop="time"
          label="时间"
        ></el-table-column>
        <el-table-column
          prop="proposer"
          label="提问人"
        ></el-table-column>
        <el-table-column
          prop="interceptType"
          label="拦截类型"
        ></el-table-column>
        <el-table-column
          prop="interceptContent"
          label="拦截内容"
        ></el-table-column>
      </el-table>
      <el-pagination
        @size-change="handleInterceptSizeChange"
        @current-change="handleInterceptCurrentChange"
        :current-page="interceptPage.currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="interceptPage.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="interceptPage.total"
      ></el-pagination>
    </el-dialog>

    <!-- 批量导入弹窗 -->
    <!-- <el-dialog
      :title="'批量导入'"
      :visible.sync="batchImportDialogVisible"
      width="600px"
      append-to-body
    >
      <el-form label-width="120px">
        <el-form-item label="敏感词类型：">
          <el-select
            v-model="importForm.type"
            placeholder="请选择"
          >
            <el-option
              v-for="item in matchTypes"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="选择文件：">
          <el-upload
            ref="upload"
            action=""
            :auto-upload="false"
            :on-change="handleFileChange"
            :file-list="fileList"
          >
            <el-button
              slot="trigger"
              size="small"
              type="primary"
              >选择文件</el-button
            >
            <div
              slot="tip"
              class="el-upload__tip"
            ></div>
          </el-upload>
        </el-form-item>
      </el-form>
      <span
        slot="footer"
        class="dialog-footer"
      >
        <el-button @click="batchImportDialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="submitBatchImport"
          :loading="importLoading"
          >提交</el-button
        >
      </span>
    </el-dialog> -->
  </div>
</template>

<script>
import { getSensitiveWordPage, saveOrUpdateSensitiveWord, batchAddSameType, typeStatPage } from '@/common/api/data';
export default {
  data() {
    return {
      loading: false,
      sensitiveList: [
        {
          wordType: '暴力',
          sensitiveWord: 'XXX',
          matchMode: '短语匹配',
          interceptCount: 5,
          createTime: '2025-03-12 12:12:12',
        },
        {
          wordType: '党风',
          sensitiveWord: 'XXX',
          matchMode: '精准匹配',
          interceptCount: 7,
          createTime: '2025-03-12 12:12:12',
        },
        {
          wordType: '涉黄',
          sensitiveWord: 'XXX',
          matchMode: '模糊匹配',
          interceptCount: 9,
          createTime: '2025-03-12 12:12:12',
        },
      ],
      form: {},
      fields: [],
      columns: [
        { prop: 'wordType', label: '类型' },
        { prop: 'wordCount', label: '敏感词' },
        { prop: 'matchMode', label: '匹配方式' },
        {
          prop: 'interceptCount',
          label: '拦截数量',
          scopedSlots: {
            default: (scope) => {
              return scope.row.interceptCount > 0 ? (
                <div
                  onClick={() => {
                    this.handleInterceptInfo(scope.row.id);
                  }}
                  class="intercept-count"
                >
                  <span class="count-text">{scope.row.interceptCount}</span>
                </div>
              ) : (
                <div>0</div>
              );
            },
          },
        },
        { prop: 'createTime', label: '创建时间' },
        {
          label: '操作',
          scopedSlots: {
            default: (scope) => {
              return (
                <span>
                  <el-button
                    size="mini"
                    onClick={() => this.deleteItem(scope.row)}
                  >
                    删除
                  </el-button>
                  <el-button
                    size="mini"
                    onClick={() => this.editItem(scope.row)}
                  >
                    编辑
                  </el-button>
                </span>
              );
            },
          },
        },
      ],
      total: 0,
      currentPage: 1,
      pageSize: 10,
      commonDialogVisible: false,
      dialogMode: 'add',
      interceptDialogVisible: false,
      interceptDetails: [],
      batchImportDialogVisible: false,
      importForm: {
        wordType: '',
      },
      fileList: [],
      importLoading: false,
      selectedFile: null,
      interceptPage: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },
      currentForm: {
        wordType: '',
        sensitiveWord: '',
        matchMode: '',
      },
      matchModes: [
        { value: '短语匹配', label: '短语匹配' },
        { value: '精准匹配', label: '精准匹配' },
        { value: '模糊匹配', label: '模糊匹配' },
      ],
      addRules: {
        wordType: [{ required: true, message: '请输入敏感词类型', trigger: 'blur' }],
        sensitiveWord: [{ required: true, message: '请输入敏感词', trigger: 'blur' }],
        matchMode: [{ required: true, message: '请选择匹配方式', trigger: 'change' }],
      },
      editRules: {
        wordType: [{ required: true, message: '请输入敏感词类型', trigger: 'blur' }],
        sensitiveWord: [{ required: true, message: '请输入敏感词', trigger: 'blur' }],
        matchMode: [{ required: true, message: '请选择匹配方式', trigger: 'change' }],
      },
      originalForm: {
        wordType: '',
        sensitiveWord: '',
        matchMode: '',
      },
    };
  },

  computed: {
    dialogTitle() {
      return this.dialogMode === 'add' ? '新增敏感词' : '编辑敏感词';
    },
    currentRules() {
      return this.dialogMode === 'add' ? this.addRules : this.editRules;
    },
  },
  mounted() {
    this.queryTable();
    this.queryTopData();
  },
  methods: {
    openAddDialog() {
      this.dialogMode = 'add';
      this.currentForm = {
        wordType: '',
        sensitiveWord: '',
        matchMode: '',
      };
      this.commonDialogVisible = true;
      this.$nextTick(() => {
        if (this.$refs['commonForm']) {
          this.$refs['commonForm'].clearValidate();
        }
      });
    },
    async queryTable() {
      try {
        this.loading = true;

        // 构造符合接口要求的请求参数格式
        const requestParams = {
          page: {
            pageNum: this.currentPage,
            pageSize: this.pageSize,
          },
        };

        const res = await getSensitiveWordPage(requestParams);

        this.loading = false;

        if (res && res.code === 200) {
          // 获取基础数据
          const wordList = res.data?.dataList || [];
          this.total = res.data?.totalCount || 0;

          // 为每个记录获取对应的敏感词数量
          const promises = wordList.map(async (item) => {
            // 构造 typeStatPage 的请求参数，按 wordType 查询
            const typeStatParams = {
              page: {
                pageNum: 0,
                pageSize: 0,
              },
              filter: {
                wordType: item.wordType, // 传入具体的 wordType
              },
            };

            const statRes = await typeStatPage(typeStatParams);
            if (statRes && statRes.code === 200 && statRes.data?.dataList?.length > 0) {
              // 返回合并后的数据，添加 wordCount 字段
              return {
                ...item,
                wordCount: statRes.data.dataList[0].wordCount || 0,
              };
            }
            // 如果没有获取到统计数据，返回原始数据，wordCount 设为 0
            return {
              ...item,
              wordCount: 0,
            };
          });

          // 等待所有请求完成
          this.sensitiveList = await Promise.all(promises);
        } else {
          this.$message.error(res?.msg || '获取数据失败');
        }
      } catch (error) {
        console.error('获取表格数据失败:', error);
        this.$message.error('获取表格数据失败');
        this.loading = false;
      }
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.queryTable();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.queryTable();
    },
    // async queryTable() {
    //   try {
    //     this.loading = true;

    //     // 构造符合接口要求的请求参数格式
    //     const requestParams = {
    //       page: {
    //         pageNum: this.currentPage,
    //         pageSize: this.pageSize,
    //       },
    //     };

    //     const res = await getSensitiveWordPage(requestParams);

    //     this.loading = false;

    //     if (res && res.code === 200) {
    //       // 根据实际响应结构调整 - 使用 dataList 和 totalCount
    //       this.sensitiveList = res.data?.dataList || [];
    //       this.total = res.data?.totalCount || 0;
    //     } else {
    //       this.$message.error(res?.msg || '获取数据失败');
    //     }
    //   } catch (error) {
    //     console.error('获取表格数据失败:', error);
    //     this.$message.error('获取表格数据失败');
    //     this.loading = false;
    //   }
    // },
    deleteItem(row) {
      this.$confirm('此操作将永久删除该敏感词, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          const index = this.sensitiveList.findIndex(
            (item) => item.type === row.type && item.sensitiveWord === row.sensitiveWord,
          );
          if (index !== -1) {
            this.sensitiveList.splice(index, 1);
            this.$message.success('删除成功');
          }
        })
        .catch(() => {
          this.$message.info('已取消删除');
        });
    },
    editItem(row) {
      this.dialogMode = 'edit';
      // 保存原始数据用于提交时查找
      this.originalForm = {
        id: row.id, // 保存ID用于编辑
        wordType: row.wordType,
        sensitiveWord: row.sensitiveWord,
        matchMode: row.matchMode,
      };
      // 当前表单数据
      this.currentForm = {
        wordType: row.wordType,
        sensitiveWord: row.sensitiveWord,
        matchMode: row.matchMode,
      };
      this.commonDialogVisible = true;
      this.$nextTick(() => {
        if (this.$refs['commonForm']) {
          this.$refs['commonForm'].clearValidate();
        }
      });
    },

    initCommonDialog() {
      // 初始化弹窗逻辑
    },
    pushDialogClose() {
      this.currentForm = {
        wordType: '',
        sensitiveWord: '',
        matchMode: '',
      };
      this.$nextTick(() => {
        if (this.$refs['commonForm']) {
          this.$refs['commonForm'].clearValidate();
        }
      });
    },
    submitCommonForm() {
      this.$refs['commonForm'].validate((valid) => {
        if (valid) {
          if (this.dialogMode === 'add') {
            this.submitAdd();
          } else {
            this.submitEdit();
          }
        }
      });
    },

    async submitAdd() {
      try {
        const requestData = {
          wordType: this.currentForm.wordType,
          sensitiveWord: this.currentForm.sensitiveWord,
          matchMode: this.currentForm.matchMode,
        };

        const res = await saveOrUpdateSensitiveWord(requestData);

        if (res && res.code === 200) {
          this.commonDialogVisible = false;
          this.$message.success('新增敏感词成功');
          this.queryTable(); // 重新加载数据
        } else {
          this.$message.error(res?.msg || '新增敏感词失败');
        }
      } catch (error) {
        console.error('新增敏感词失败:', error);
        this.$message.error('新增敏感词失败');
      }
    },

    async submitEdit() {
      try {
        // 构造批量新增同类型的请求数据
        const requestData = {
          wordType: this.currentForm.wordType,
          sensitiveWords: [this.currentForm.sensitiveWord], // 将单个词放入数组中
          matchMode: this.currentForm.matchMode,
        };

        const res = await batchAddSameType(requestData);

        if (res && res.code === 200) {
          this.commonDialogVisible = false;
          this.$message.success('编辑敏感词成功');
          this.queryTable(); // 重新加载数据
        } else {
          this.$message.error(res?.msg || '编辑敏感词失败');
        }
      } catch (error) {
        console.error('编辑敏感词失败:', error);
        this.$message.error('编辑敏感词失败');
      }
    },
    handleInterceptInfo(id) {
      // 根据 id 获取拦截详情数据
      this.getInterceptDetails(id);
      this.interceptDialogVisible = true;
    },
    getInterceptDetails() {
      // 模拟获取拦截详情数据的逻辑
      this.interceptDetails = [
        {
          time: '2025-12-12 12:12:12',
          proposer: '工号',
          interceptType: '提问拦截',
          interceptContent: 'XXXXXXXXXXXXX',
        },
        {
          time: '2025-12-12 12:12:12',
          proposer: '工号',
          interceptType: '回复拦截',
          interceptContent: 'XXXXXXXXXXXXX',
        },
      ];
      this.interceptPage.total = this.interceptDetails.length;
    },
    handleInterceptSizeChange(val) {
      this.interceptPage.pageSize = val;
      this.getInterceptDetails(this.currentId); // 假设 currentId 是当前选中的 id
    },
    handleInterceptCurrentChange(val) {
      this.interceptPage.currentPage = val;
      this.getInterceptDetails(this.currentId); // 假设 currentId 是当前选中的 id
    },
    // batchImport() {
    //   this.batchImportDialogVisible = true;
    // },
    // submitBatchImport() {
    //   if (!this.selectedFile) {
    //     this.$message.warning('请先选择文件');
    //     return;
    //   }
    //   this.importLoading = true;
    //   // 这里添加文件上传和处理的逻辑
    //   setTimeout(() => {
    //     this.importLoading = false;
    //     this.batchImportDialogVisible = false;
    //     this.$message.success('批量导入成功');
    //     // 重新加载敏感词列表
    //     this.queryTable();
    //   }, 2000); // 模拟异步操作
    // },
    handleFileChange(file, fileList) {
      this.fileList = fileList;
      this.selectedFile = file.raw;
    },
  },
};
</script>
<style lang="scss" scoped>
.mb-10 {
  margin-bottom: 10px;
}

.el-table .cell.operation {
  width: 200px !important;
}

.el-pagination {
  margin-top: 20px;
  text-align: center;
}

.el-dialog {
  .el-form-item {
    margin-bottom: 22px;
  }
}

.intercept-count {
  cursor: pointer;

  .count-text {
    color: #409eff; // 蓝色
    transition: color 0.3s, background-color 0.3s;

    &:hover {
      color: #66b1ff;
      background-color: #ecf5ff;
      padding: 2px 4px;
      border-radius: 4px;
    }
  }
}
</style>
