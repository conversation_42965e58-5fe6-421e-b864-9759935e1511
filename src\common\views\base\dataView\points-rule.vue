<template>
  <div>
    <!-- <div class="realtime-header mb-10">
      <base-tips
        title="积分规则"
        class="mb-10"
      >
        <div slot="title">
          <span>积分规则</span>
        </div>
      </base-tips>
    </div>
    <base-card class="mb-10">
      <div class="flex top-container">
        <statistical-cards
          title="今日|积分发放数量"
          :value="topData"
          icon="el-icon-s-data"
        ></statistical-cards>
      </div>
    </base-card> -->
    <base-tips
      title="详细数据"
      class="mb-10"
    ></base-tips>
    <el-button
      @click="openPushDialog"
      style="margin-bottom: 10px"
      >新建规则</el-button
    >
    <search-table
      :form="form"
      :fields="fields"
      :columns="columns"
      :data="dataList"
      :query="query"
      :total="total"
      :auto-height="true"
      min-height="200px"
      max-height="600px"
      :row-height="40"
    >
      <!-- <el-button
        slot="after"
        @click="downloadFile"
        >导出</el-button
      >
      <el-button
        slot="after"
        @click="downloadFile2"
        >导出-部门积分</el-button
      >
      <el-button
        slot="after"
        @click="downloadFile3"
        >导出-个人积分</el-button
      > -->
    </search-table>
    <!-- 新建推送 dialog -->
    <!-- 添加或修改反馈对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="pushVisible"
      @close="pushDialogClose"
      width="500px"
      append-to-body
      :close-on-click-modal="clickfalse"
      :close-on-press-escape="false"
    >
      <el-form
        ref="form"
        :model="form"
        :rules="formRules"
        label-width="120px"
      >
        <el-form-item
          label="积分编码"
          prop="pointsCode"
        >
          <el-input
            v-model="form.pointsCode"
            placeholder="请输入积分编码"
          />
        </el-form-item>
        <el-form-item
          label="积分名称"
          prop="pointsName"
        >
          <el-input
            v-model="form.pointsName"
            placeholder="请输入积分名称"
          />
        </el-form-item>
        <el-form-item
          label="单次积分"
          prop="pointsPerTransaction"
        >
          <el-input
            v-model="form.pointsPerTransaction"
            placeholder="请输入单次积分"
          />
        </el-form-item>
        <el-form-item
          label="每日获取积分次数"
          prop="dailyPointsEarningLimit"
        >
          <el-input
            v-model="form.dailyPointsEarningLimit"
            placeholder="每日获取积分次数，-1时无上限"
          />
        </el-form-item>
        <el-form-item
          label="任务描述"
          prop="description"
        >
          <el-input
            v-model="form.description"
            placeholder="请输入积分任务描述"
          />
        </el-form-item>
        <!-- 后续参数等待后台合并确认 -->
        <!-- <el-form-item
          label="删除标志"
          prop="delFlag"
        >
          <el-input
            v-model="form.delFlag"
            placeholder="删除标志（0代表存在 2代表删除）"
          />
        </el-form-item>
        <el-form-item
          label="状态"
          prop="status"
        >
          <el-input
            v-model="form.status"
            placeholder="	状态（0正常 1停用）"
          />
        </el-form-item>
        <el-form-item
          label="版本"
          prop="version"
        >
          <el-input
            v-model="form.version"
            placeholder="请输入版本号"
          />
        </el-form-item>
        <el-form-item
          label="创建者"
          prop="createBy"
        >
          <el-input
            v-model="form.createBy"
            placeholder="创建者"
          />
        </el-form-item>
        <el-form-item
          label="创建者名称"
          prop="createName"
        >
          <el-input
            v-model="form.createName"
            placeholder="创建者名称"
          />
        </el-form-item>
        <el-form-item
          label="创建时间"
          v-if="!edit"
        >
          <el-date-picker
            style="width: 100%"
            clearable
            v-model="userSyncTime"
            @change="userSyncTimeChange"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item
          label="更新者"
          prop="updateBy"
        >
          <el-input
            v-model="form.updateBy"
            placeholder="更新者"
          />
        </el-form-item>
        <el-form-item
          label="更新者名称"
          prop="updateName"
        >
          <el-input
            v-model="form.updateName"
            placeholder="更新者名称"
          />
        </el-form-item>
        <el-form-item
          label="更新时间"
          v-if="!edit"
        >
          <el-date-picker
            style="width: 100%"
            clearable
            v-model="userSyncTime"
            @change="userSyncTimeChange"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item
          label="推送用户"
          prop="userIds"
          v-loading="userTreeLogin"
        >
          <el-cascader
            :show-all-levels="false"
            v-model="form.userIds"
            ref="cascader"
            :options="cascaderOptions"
            :props="props"
            collapse-tags
            clearable
            @change="handleChange"
            filterable
          ></el-cascader>
        </el-form-item>
        <el-form-item
          label="任务积分"
          prop="points"
        >
          <el-input
            v-model="form.points"
            placeholder="请输入任务积分"
          />
        </el-form-item> -->
      </el-form>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          type="primary"
          @click="submitForm"
          v-if="!edit"
          >新 增</el-button
        >
        <el-button
          type="primary"
          @click="submitForm"
          v-if="edit"
          >编 辑
        </el-button>
        <el-button @click="pushVisible = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { pointstaskAddEdit, pointstaskList } from '@/common/api/data';
export default {
  data() {
    const query = this.queryTable;
    return {
      title: '新建规则',
      dataList: [],
      clickfalse: false,
      query,
      data: [],
      topData: 0,
      columns: [
        {
          prop: 'pointsCode',
          label: '积分编码',
        },
        {
          prop: 'pointsName',
          label: '积分名称',
        },
        {
          prop: 'pointsPerTransaction',
          label: '单次积分',
        },
        {
          prop: 'dailyPointsEarningLimit',
          label: '每日获取积分次数',
        },
        {
          prop: 'description',
          label: '积分任务描述',
        },
        {
          label: '操作',
          scopedSlots: {
            default: (scope) => {
              return (
                <el-button
                  type="text"
                  onClick={() => {
                    this.openDetail(scope.row);
                  }}
                >
                  编辑
                </el-button>
              );
            },
          },
        },
        // {
        //   prop: 'delFlag',
        //   label: '删除标志',
        // },
        // {
        //   prop: 'status',
        //   label: '状态',
        // },
        // {
        //   prop: 'points',
        //   label: '任务积分',
        // },
        // {
        //   prop: 'taskStatus',
        //   label: '任务状态',
        // },
      ],
      fields: [],
      form: {
        id: '',
        pointsCode: '',
        pointsName: '',
        pointsPerTransaction: '',
        dailyPointsEarningLimit: '',
        description: '',
      },
      formRules: {
        pointsCode: [{ required: true, message: '请输入积分编码', trigger: 'blur' }],
        pointsName: [{ required: true, message: '请输入积分名称', trigger: 'blur' }],
        pointsPerTransaction: [{ required: true, message: '请输入单次积分', trigger: 'blur' }],
        dailyPointsEarningLimit: [{ required: true, message: '请输入每日获取积分次数', trigger: 'blur' }],
        description: [{ required: true, message: '请输入积分任务描述', trigger: 'blur' }],
      },
      total: 0,
      realtimeText: '',
      onlineUser: 456,
      userOptions: [], // 存储用户选项
      pushVisible: false,
      edit: false,
    };
  },
  async mounted() {
    // 初始加载一些用户数据
    this.queryTable();
  },
  methods: {
    openDetail(row) {
      this.pushVisible = true;
      this.title = '编辑规则';
      this.edit = true;
      // console.log(row);
      // const userIds = row.userIds.split('|');
      this.form = {
        id: row.id ? row.id : '',
        pointsCode: row.pointsCode,
        pointsName: row.pointsName,
        pointsPerTransaction: row.pointsPerTransaction,
        dailyPointsEarningLimit: row.dailyPointsEarningLimit,
        description: row.description,
      };
    },
    openPushDialog() {
      this.pushVisible = true;
      this.edit = false;
      this.$nextTick(() => {
        this.$refs['form'].clearValidate();
      });
    },
    //关闭dialog
    pushDialogClose() {
      this.form = {};
      this.userSyncTime = [];
      this.$nextTick(() => {
        this.$refs['form'].clearValidate();
      });
    },
    //新增推送 dialog框确定
    submitForm() {
      this.$refs['form'].validate(async (valid) => {
        if (!valid) {
          console.log('error submit!!');
          return false;
        }

        // 组装数据
        // this.form.userIds = this.form.userIds.join('|');
        const res = await pointstaskAddEdit(this.form);

        if (res.code !== 200) return this.$modal.msgError(res.message);
        this.$modal.msgSuccess('新增成功！');
        // this.getList();
        // this.resetForm();
        this.pushVisible = false;
        this.queryTable();
        // console.log(res);
      });
    },
    /**搜索table数据 */
    async queryTable() {
      const res = await pointstaskList({});
      if (res && res.code === 200) {
        this.dataList = res.data.map((item) => {
          return {
            ...item,
          };
        });
        this.total = res.data.totalCount;
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.top-container {
  gap: 10px;

  // .top-item {
  //   width: 25%;
  //   height: 100px;
  // }
}

.mb-10 {
  margin-bottom: 10px;
}

.realtime-info {
  color: #909399;
  margin-left: 10px;
  font-size: 14px;
  vertical-align: middle;
}
.chartBox {
  overflow: hidden;
  margin-top: 10px;
  .chartBoxContent {
    width: calc(50% - 5px);
    float: left;
    &:nth-child(odd) {
      margin-right: 10px;
    }
  }
}
</style>
