<template>
  <div>
    <base-tips
      title="大屏维护"
      class="mb-10"
    >
      <div slot="title">
        <span>大屏维护</span>
      </div>
    </base-tips>

    <base-card class="mb-10">
      <div class="edit-item">
        <label style="width: 160px">模型服务单位数：</label>
        <el-input
          v-model="form.modelService"
          placeholder="模型服务单位数"
          :disabled="!fileEditMode"
          style="width: 260px; margin-right: 10px"
        ></el-input>
        <el-button
          :type="fileEditMode ? 'primary' : 'default'"
          @click="toggleFileEditMode"
        >
          {{ fileEditMode ? '保存' : '编辑' }}
        </el-button>
      </div>

      <div class="edit-item">
        <label style="width: 160px">算力服务单位数：</label>
        <el-input
          v-model="form.computeService"
          placeholder="算力服务单位数"
          :disabled="!knowledgeEditMode"
          style="width: 260px; margin-right: 10px"
        ></el-input>
        <el-button
          :type="knowledgeEditMode ? 'primary' : 'default'"
          @click="toggleKnowledgeEditMode"
        >
          {{ knowledgeEditMode ? '保存' : '编辑' }}
        </el-button>
      </div>

      <div class="edit-item">
        <label style="width: 160px">算力总使用量（台）：</label>
        <el-input
          v-model="form.computeNum"
          placeholder="算力总使用量（台）"
          :disabled="!agentEditMode"
          style="width: 260px; margin-right: 10px"
        ></el-input>
        <el-button
          :type="agentEditMode ? 'primary' : 'default'"
          @click="toggleAgentEditMode"
        >
          {{ agentEditMode ? '保存' : '编辑' }}
        </el-button>
      </div>
    </base-card>
  </div>
</template>

<script>
import { getScreenData, updateScreenData } from '@/common/api/data';
export default {
  data() {
    return {
      form: {
        modelService: '0',
        computeService: '0',
        computeNum: '0',
      },
      fileEditMode: false,
      knowledgeEditMode: false,
      agentEditMode: false,
      // 新增：存储各类型数据的id
      ids: {
        modelService: '',
        computeService: '',
        computeNum: '',
      },
    };
  },
  mounted() {
    this.loadScreenData();
  },
  methods: {
    loadScreenData() {
      getScreenData({})
        .then((res) => {
          if (res && res.code === 200 && res.data && res.data.length > 0) {
            // 遍历返回的数据，根据dashboardType设置对应的值和id
            res.data.forEach((item) => {
              switch (item.dashboardType) {
                case 'modelService':
                  this.form.modelService = item.dashboardValue;
                  this.ids.modelService = item.id;
                  break;
                case 'computeService':
                  this.form.computeService = item.dashboardValue;
                  this.ids.computeService = item.id;
                  break;
                case 'computeNum':
                  this.form.computeNum = item.dashboardValue;
                  this.ids.computeNum = item.id;
                  break;
                default:
                  break;
              }
            });
          }
        })
        .catch((error) => {
          this.$message.error('获取大屏数据失败');
          console.error('获取大屏数据失败:', error);
        });
    },

    toggleFileEditMode() {
      if (this.fileEditMode) {
        // 保存逻辑 - 传入id和dashboardValue
        updateScreenData({
          id: this.ids.modelService,
          dashboardValue: this.form.modelService,
        })
          .then((res) => {
            if (res && res.code === 200) {
              this.$message.success('模型服务单位数保存成功');
            } else {
              this.$message.error('保存失败，请重试');
            }
          })
          .catch((error) => {
            this.$message.error('保存失败，请重试');
            console.error('保存模型服务单位数失败:', error);
          });
      }
      this.fileEditMode = !this.fileEditMode;
    },

    toggleKnowledgeEditMode() {
      if (this.knowledgeEditMode) {
        // 保存逻辑 - 传入id和dashboardValue
        updateScreenData({
          id: this.ids.computeService,
          dashboardValue: this.form.computeService,
        })
          .then((res) => {
            if (res && res.code === 200) {
              this.$message.success('算力服务单位数保存成功');
            } else {
              this.$message.error('保存失败，请重试');
            }
          })
          .catch((error) => {
            this.$message.error('保存失败，请重试');
            console.error('保存算力服务单位数失败:', error);
          });
      }
      this.knowledgeEditMode = !this.knowledgeEditMode;
    },

    toggleAgentEditMode() {
      if (this.agentEditMode) {
        // 保存逻辑 - 传入id和dashboardValue
        updateScreenData({
          id: this.ids.computeNum,
          dashboardValue: this.form.computeNum,
        })
          .then((res) => {
            if (res && res.code === 200) {
              this.$message.success('算力总使用量保存成功');
            } else {
              this.$message.error('保存失败，请重试');
            }
          })
          .catch((error) => {
            this.$message.error('保存失败，请重试');
            console.error('保存算力总使用量失败:', error);
          });
      }
      this.agentEditMode = !this.agentEditMode;
    },
  },
};
</script>

<style lang="scss" scoped>
.mb-10 {
  margin-bottom: 10px;
}

.edit-item {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  label {
    width: 100px;
    text-align: right;
    margin-right: 10px;
  }
}
</style>
