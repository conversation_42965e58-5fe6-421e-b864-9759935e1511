<template>
  <div>
    <div class="realtime-header mb-10">
      <base-tips
        title="实时统计"
        class="mb-10"
      >
        <div slot="title">
          <span>实时统计</span>
        </div>
      </base-tips>
    </div>
    <base-card class="mb-10">
      <div class="flex top-container">
        <statistical-cards
          title="今日|累计新用户"
          :value="topData.newUserCount"
          icon="el-icon-s-data"
        ></statistical-cards>
        <statistical-cards
          title="今日|累计活跃老用户数"
          :value="topData.longUserCount"
          icon="el-icon-s-data"
          bg="linear-gradient(160deg, #8f69ca, #483c9b)"
        ></statistical-cards>
        <statistical-cards
          title="今日|累计日活用户数"
          :value="topData.totalUserCount"
          icon="el-icon-s-data"
          bg="linear-gradient(160deg, #57d6f2, #4b97e8)"
        ></statistical-cards>
        <statistical-cards
          title="今日|累计访问次数"
          :value="topData.totalVisitCount"
          icon="el-icon-s-data"
          bg="linear-gradient(160deg, #fbc927, #f67852)"
        ></statistical-cards>
      </div>
    </base-card>
    <base-tips
      title="详细数据"
      class="mb-10"
    ></base-tips>
    <search-table
      :form="form"
      :fields="fields"
      :columns="columns"
      :data="data"
      :query="query"
      :total="total"
    >
      <el-button
        slot="after"
        @click="downloadFile"
        >导出</el-button
      >
    </search-table>
    <div class="chartBox">
      <div class="chartBoxContent">
        <base-tips
          title="活跃用户趋势"
          class="mb-10"
        ></base-tips>
        <echart-bar
          :options="chartOptions"
          ref="userChart"
        ></echart-bar>
      </div>
      <div class="chartBoxContent">
        <base-tips
          title="累计访问次数趋势"
          class="mb-10"
        ></base-tips>
        <echart-bar
          :options="visitChartOptions"
          ref="visitChart"
        ></echart-bar>
      </div>
    </div>
  </div>
</template>

<script>
import statisticalCards from '@/common/components/statisticalCards/statisticalCards.vue';
import echartBar from '@/common/components/echart/echartBar.vue';
import {
  getRealDataList,
  getRealTodayDataInfo,
  downloadRealData,
  getUserChartsStats,
  getVisitChartsStats,
} from '@/common/api/data';
import { SOURCE } from '@/common/utils/constant';
import { download } from '@/common/utils/index';
import { formatDate } from 'element-ui/lib/utils/date-util';

const now = new Date().getTime();
const start = now - 60 * 24 * 60 * 60 * 1000;
const startTime = formatDate(start, 'yyyy-MM-dd');
const endTime = formatDate(now - 1 * 24 * 60 * 60 * 1000, 'yyyy-MM-dd');
export default {
  components: { statisticalCards, echartBar },
  data() {
    const query = this.queryTable;
    return {
      startTime: '',
      endTime: '',
      visitFrom: '',
      chartOptions: {
        title: {
          text: '',
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985',
            },
          },
        },
        legend: {
          data: ['活跃用户总数', '活跃老用户', '活跃新用户'],
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '5%',
          containLabel: true,
        },
        // 添加dataZoom组件，实现X轴滑动功能
        dataZoom: [
          {
            type: 'slider', // 滑动条型
            show: true,
            xAxisIndex: [0],
            start: 0,
            end: 100,
            height: 20,
            bottom: 0,
            borderColor: 'rgba(0,0,0,0.2)',
            fillerColor: 'rgba(167,183,204,0.4)',
            handleIcon:
              'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
            handleSize: '80%',
            handleStyle: {
              color: '#fff',
              shadowBlur: 3,
              shadowColor: 'rgba(0, 0, 0, 0.6)',
              shadowOffsetX: 2,
              shadowOffsetY: 2,
            },
          },
          {
            type: 'inside', // 内置型，支持鼠标滚轮缩放
            xAxisIndex: [0],
            start: 0,
            end: 100,
          },
        ],
        xAxis: {
          type: 'category',
          boundaryGap: true,
          data: [],
          axisLabel: {
            rotate: 45, // 标签旋转45度，避免文字重叠
            interval: 'auto',
            formatter: function (value) {
              return value; // 直接返回已经格式化的值
            },
            textStyle: {
              fontSize: 12,
            },
          },
          axisTick: {
            alignWithLabel: true,
          },
        },
        yAxis: {
          type: 'value',
          name: '用户数',
          min: 0,
          splitLine: {
            show: true,
            lineStyle: {
              type: 'dashed',
            },
          },
          axisLabel: {
            formatter: function (value) {
              return Math.round(value); // 四舍五入为整数
            },
          },
        },
        series: [
          {
            name: '活跃用户总数',
            type: 'bar',
            barWidth: '40%',
            data: [],
            itemStyle: {
              color: '#5470c6',
            },
          },
          {
            name: '活跃老用户',
            type: 'line',
            smooth: false, // 使折线图变为直线
            data: [],
            itemStyle: {
              color: '#91cc75',
            },
          },
          {
            name: '活跃新用户',
            type: 'line',
            smooth: false, // 使折线图变为直线
            data: [],
            itemStyle: {
              color: '#ee6666',
            },
          },
        ],
      },
      visitChartOptions: {
        title: {
          text: '',
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985',
            },
          },
        },
        legend: {
          data: ['累计访问次数', '电投壹PC', '电投壹移动', '内网门户'],
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '5%',
          containLabel: true,
        },
        // 添加dataZoom组件，实现X轴滑动功能
        dataZoom: [
          {
            type: 'slider',
            show: true,
            xAxisIndex: [0],
            start: 0,
            end: 100,
            height: 20,
            bottom: 0,
            borderColor: 'rgba(0,0,0,0.2)',
            fillerColor: 'rgba(246,120,82,0.2)',
            handleIcon:
              'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
            handleSize: '80%',
            handleStyle: {
              color: '#fff',
              shadowBlur: 3,
              shadowColor: 'rgba(0, 0, 0, 0.6)',
              shadowOffsetX: 2,
              shadowOffsetY: 2,
            },
          },
          {
            type: 'inside',
            xAxisIndex: [0],
            start: 0,
            end: 100,
          },
        ],
        xAxis: {
          type: 'category',
          boundaryGap: true,
          data: [],
          axisLabel: {
            rotate: 45,
            interval: 'auto',
            formatter: function (value) {
              return value; // 直接返回已经格式化的值
            },
            textStyle: {
              fontSize: 12,
            },
          },
          axisTick: {
            alignWithLabel: true,
          },
        },
        yAxis: {
          type: 'value',
          name: '访问次数',
          min: 0,
          splitLine: {
            show: true,
            lineStyle: {
              type: 'dashed',
            },
          },
          axisLabel: {
            formatter: function (value) {
              return Math.round(value); // 四舍五入为整数
            },
          },
        },
        series: [
          {
            name: '累计访问次数',
            type: 'bar',
            barWidth: '40%',
            data: [],
            itemStyle: {
              color: '#5470c6',
            },
          },
          {
            name: '电投壹PC',
            type: 'line',
            smooth: false, // 修改为false，使折线图变为直线
            data: [],
            itemStyle: {
              color: '#91cc75',
            },
          },
          {
            name: '电投壹移动',
            type: 'line',
            smooth: false, // 修改为false，使折线图变为直线
            data: [],
            itemStyle: {
              color: '#6ba364',
            },
          },
          {
            name: '内网门户',
            type: 'line',
            smooth: false, // 修改为false，使折线图变为直线
            data: [],
            itemStyle: {
              color: '#eabf5a',
            },
          },
        ],
      },
      query,
      data: [],
      topData: {
        newUserCount: 0,
        longUserCount: 0,
        totalUserCount: 0,
        totalVisitCount: 0,
      },
      columns: [
        {
          prop: 'visitDate',
          label: '日期',
        },
        {
          prop: 'newUserCount',
          label: '当日新用户数',
        },
        {
          prop: 'longUserCount',
          label: '当日活跃老用户数',
        },
        {
          prop: 'totalUserCount',
          label: '当日日活用户数',
        },
        {
          prop: 'totalVisitCount',
          label: '累计访问次数',
        },
        {
          prop: 'visitFromName',
          label: '访问来源',
        },
      ],
      fields: [
        {
          tag: 'el-date-picker',
          name: 'time',
          label: '查询时间',
          type: 'daterange',
          valueFormat: 'yyyy-MM-dd',
          pickerOptions: {
            disabledDate: this.disabledDate,
            onPick: this.onDatePick,
          },
        },
        {
          tag: 'el-select',
          name: 'visitFrom',
          label: '访问来源',
          type: 'select',
          childTag: 'el-option',
          options: SOURCE,
        },
      ],
      form: {
        time: [startTime, endTime],
        visitFrom: '',
      },
      total: 0,
      realtimeText: '',
      onlineUser: 456,
      firstPickedDate: null, // 用于跟踪第一个选择的日期
    };
  },
  mounted() {
    this.setDefaultDateRange();
    this.queryTopData();
  },
  methods: {
    // 设置默认时间范围为最近60天
    setDefaultDateRange() {
      this.form.time = [startTime, endTime];
    },

    // 日期选择限制：最多选择60天
    disabledDate(date) {
      const _now = new Date();
      const today = new Date(_now.getFullYear(), _now.getMonth(), _now.getDate());

      // 禁用未来日期
      if (date > today) {
        return true;
      }

      // 如果已经选择了第一个日期，限制第二个日期的范围
      if (this.firstPickedDate) {
        const firstDate = new Date(this.firstPickedDate);
        const diffTime = Math.abs(date - firstDate);
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        // 限制选择范围不超过60天
        return diffDays > 59;
      }

      return false;
    },

    // 日期选择回调
    onDatePick({ maxDate, minDate }) {
      this.firstPickedDate = minDate;

      // 如果选择了完整的日期范围，清除第一个选择的日期
      if (maxDate) {
        this.firstPickedDate = null;
      }
    },
    // 处理用户图表数据
    processUserChartData(res) {
      if (res && res.data) {
        const xAxisData = [];
        const userTotalData = [];
        const userLongData = [];
        const userNewData = [];

        // 收集所有数据
        res.data.forEach((item) => {
          xAxisData.push(this.formatDateString(item.date));
          userTotalData.push(item.totalUserCount);
          userLongData.push(item.longUserCount);
          userNewData.push(item.newUserCount);
        });

        // 找出所有用户数据中的最大值，用于设置Y轴最大值
        const allUserData = [...userTotalData, ...userLongData, ...userNewData];
        const maxUserCount = Math.ceil(Math.max(...allUserData) * 1.2) || 100; // 使用Math.ceil确保为整数

        // 更新用户趋势图表
        this.updateChartOptions(
          'chartOptions',
          xAxisData,
          [
            { data: userTotalData, index: 0 },
            { data: userLongData, index: 1 },
            { data: userNewData, index: 2 },
          ],
          [{ max: maxUserCount, index: 0 }],
          'userChart',
        );
      }
    },

    // 处理访问图表数据
    processVisitChartData(res) {
      if (res && res.data) {
        const xAxisData = [];
        const totalVisits = [];
        const mobileCount = [];
        const inetCount = [];
        const dtyCount = [];

        // 收集所有数据
        res.data.forEach((item) => {
          xAxisData.push(this.formatDateString(item.date));
          totalVisits.push(item.totalVisits);
          mobileCount.push(item.mobileCount);
          inetCount.push(item.inetCount);
          dtyCount.push(item.dtyCount);
        });

        // 更新访问次数图表
        this.updateChartOptions(
          'visitChartOptions',
          xAxisData,
          [
            { data: totalVisits, index: 0 },
            { data: dtyCount, index: 1 },
            { data: mobileCount, index: 2 },
            { data: inetCount, index: 3 },
          ],
          [{ max: Math.ceil(Math.max(...totalVisits) * 1.2) || 100, index: 0 }], // 使用Math.ceil确保为整数
          'visitChart',
        );
      }
    },

    // 通用的图表更新函数
    updateChartOptions(chartName, xAxisData, seriesData, yAxisData, refName) {
      const chartOptions = { ...this[chartName] };

      // 更新X轴数据
      chartOptions.xAxis = {
        ...chartOptions.xAxis,
        data: xAxisData,
      };

      // 更新Y轴数据
      if (Array.isArray(chartOptions.yAxis)) {
        yAxisData.forEach(({ max, index }) => {
          chartOptions.yAxis[index] = {
            ...chartOptions.yAxis[index],
            max,
          };
        });
      } else if (yAxisData.length > 0) {
        chartOptions.yAxis = {
          ...chartOptions.yAxis,
          max: yAxisData[0].max,
        };
      }

      // 更新系列数据
      seriesData.forEach(({ data, index }) => {
        chartOptions.series[index] = {
          ...chartOptions.series[index],
          data,
        };
      });

      // 更新dataZoom
      chartOptions.dataZoom = chartOptions.dataZoom.map((zoom) => ({
        ...zoom,
        start: 0,
        end: 100,
      }));

      // 更新图表配置
      this[chartName] = chartOptions;

      // 强制更新图表
      this.$nextTick(() => {
        if (this.$refs[refName] && this.$refs[refName].updateChart) {
          this.$refs[refName].updateChart();
        }
      });
    },

    /**搜索table数据 */
    async queryTable(row) {
      // console.log('row', row);
      // 设置日期范围
      this.startTime = row.time && row.time[0] ? `${row.time[0]}` : startTime;
      this.endTime = row.time && row.time[1] ? `${row.time[1]}` : endTime;
      this.visitFrom = row.visitFrom;

      const params = {
        page: {
          pageNum: row.pageNum,
          pageSize: row.pageSize,
        },
        filter: {
          startTime: `${this.startTime} 00:00:00`,
          endTime: `${this.endTime} 23:59:59`,
          visitFrom: this.visitFrom,
        },
      };

      try {
        const res = await getRealDataList(params);
        if (res && res.code === 200) {
          this.data = res.data.dataList.map((item) => {
            const visitFromName = SOURCE.find((i) => i.value === item.visitFrom);
            return {
              ...item,
              visitFromName: visitFromName && visitFromName.label,
            };
          });
          this.total = res.data.totalCount;

          // 更新图表数据
          Promise.all([
            getUserChartsStats({
              startTime: `${this.startTime} 00:00:00`,
              endTime: `${this.endTime} 23:59:59`,
              visitFrom: this.visitFrom,
            }),
            getVisitChartsStats({
              startTime: `${this.startTime} 00:00:00`,
              endTime: `${this.endTime} 23:59:59`,
              visitFrom: this.visitFrom,
            }),
          ])
            .then(([userRes, visitRes]) => {
              this.processUserChartData(userRes);
              this.processVisitChartData(visitRes);
            })
            .catch((error) => {
              console.error('获取图表数据失败:', error);
            });
        }
      } catch (error) {
        console.error('获取表格数据失败:', error);
      }
    },
    /**顶部数据查询 */
    async queryTopData() {
      try {
        const res = await getRealTodayDataInfo();
        if (res && res.code === 200) {
          this.topData = {
            ...this.topData,
            ...res.data,
          };
        }
      } catch (error) {
        console.error('获取顶部数据失败:', error);
      }
    },
    async downloadFile() {
      const row = this.form;
      const params = {
        startTime: row.time && row.time[0] ? `${row.time[0]} 00:00:00` : `${startTime} 00:00:00`,
        endTime: row.time && row.time[1] ? `${row.time[1]} 23:59:59` : `${endTime} 23:59:59`,
        visitFrom: row.visitFrom,
      };

      try {
        const res = await downloadRealData(params);
        if (res && res.status === 200) {
          download(res.data, '实时数据-详细数据列表');
        }
      } catch (error) {
        console.error('下载文件失败:', error);
      }
    },
    formatDateString(dateStr) {
      // 如果日期格式为 yyyy-MM-dd
      if (dateStr && dateStr.length === 10 && dateStr.includes('-')) {
        // 只保留月-日部分
        return dateStr.substring(5);
      }
      return dateStr;
    },
  },
};
</script>
<style lang="scss" scoped>
.top-container {
  gap: 10px;

  // .top-item {
  //   width: 25%;
  //   height: 100px;
  // }
}

.mb-10 {
  margin-bottom: 10px;
}

.realtime-info {
  color: #909399;
  margin-left: 10px;
  font-size: 14px;
  vertical-align: middle;
}
.chartBox {
  overflow: hidden;
  margin-top: 10px;
  .chartBoxContent {
    width: calc(50% - 5px);
    float: left;
    &:nth-child(odd) {
      margin-right: 10px;
    }
  }
}
</style>
