<template>
  <div>
    <div class="realtime-header mb-10">
      <!-- <img
        src="http://************:9000/feedback/%E5%BE%AE%E4%BF%A1%E5%9B%BE%E7%89%87_20250618174229_20250618054259.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=minioadmin%2F20250619%2Fcn-north-1%2Fs3%2Faws4_request&X-Amz-Date=20250619T074910Z&X-Amz-Expires=60&X-Amz-SignedHeaders=host&X-Amz-Signature=67dbe40ec51195487f3ab625f5a5360ba9d3d16e02eebb0e57dd98045b53725a"
      /> -->
      <base-tips
        title="反馈管理"
        class="mb-10"
      >
        <div slot="title">
          <span>反馈管理</span>
        </div>
      </base-tips>
    </div>
    <base-card class="mb-10">
      <div class="flex top-container">
        <statistical-cards
          title="今日|反馈数量"
          :value="topData"
          icon="el-icon-s-data"
        ></statistical-cards>
      </div>
    </base-card>
    <base-tips
      title="详细数据"
      class="mb-10"
    ></base-tips>
    <search-table
      :form="form"
      :fields="fields"
      :columns="columns"
      :data="data"
      :query="query"
      :total="total"
      :height="800"
      @reset="reset"
    >
      <template slot="columns">
        <el-table-column
          label="图片"
          align="center"
          width="60"
        >
          <template slot-scope="{ row }">
            <div
              v-if="row.imageUrls && row.imageUrls.length > 0"
              class="image-preview-wrapper"
            >
              <el-image
                style="width: 50px; height: 50px; object-fit: cover; cursor: pointer"
                :src="row.imageUrls[0]"
                :preview-src-list="row.imageUrls"
                fit="cover"
              >
                <!-- <div
                  slot="error"
                  class="image-slot"
                >
                  <i class="el-icon-picture-outline"></i>
                </div> -->
              </el-image>
              <span
                v-if="row.imageUrls.length > 1"
                class="image-count"
                >+{{ row.imageUrls.length - 1 }}</span
              >
            </div>
            <div
              v-else
              class="noImage"
            >
              无图片
            </div>
          </template>
        </el-table-column>
      </template>

      <el-button
        slot="after"
        @click="downloadFile"
        >导出</el-button
      >
    </search-table>
    <el-dialog
      title="反馈回复"
      :visible.sync="dialogVisible"
      width="30%"
      :before-close="handleClose"
    >
      <el-form>
        <el-form-item label="回复内容">
          <el-input
            type="textarea"
            placeholder="请输入内容"
            rows="5"
            v-model="replay"
          ></el-input>
        </el-form-item>
        <el-form-item label="回复状态">
          <div>
            <el-button
              :type="responseStatus === '1' ? 'primary' : ''"
              @click="setResponseStatus('1')"
            >
              处理中
            </el-button>
            <el-button
              :type="responseStatus === '2' ? 'success' : ''"
              @click="setResponseStatus('2')"
            >
              已解决
            </el-button>
            <el-button
              :type="responseStatus === '3' ? 'danger' : ''"
              @click="setResponseStatus('3')"
            >
              暂不处理
            </el-button>
          </div>
        </el-form-item>
      </el-form>
      <span
        slot="footer"
        class="dialog-footer"
      >
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button
          type="primary"
          @click="handelSubClick"
          >确 定
        </el-button>
      </span>
    </el-dialog>
    <el-dialog
      title="回复记录"
      :visible.sync="feedbackdialog"
      width="50%"
      :before-close="handlefeedbackClose"
    >
      <el-table
        v-loading="loading"
        :data="feedbackdata"
      >
        <el-table-column
          label="回复时间"
          align="center"
          prop="createTime"
        />
        <el-table-column
          label="回复内容"
          align="center"
          prop="responseContent"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="回复人"
          align="center"
          prop="userName"
        />
      </el-table>
      <el-pagination
        background
        style="text-align: right"
        @size-change="handleFeedbackSizeChange"
        @current-change="handleFeedbackCurrentChange"
        :current-page="feedbackPage"
        :page-sizes="[10, 20]"
        :page-size="feedbackPageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="feedbacktotal"
      >
      </el-pagination>
      <span
        slot="footer"
        class="dialog-footer"
      >
        <el-button @click="feedbackdialog = false">取 消</el-button>
        <el-button
          type="primary"
          @click="handelSubClick"
          >确 定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import statisticalCards from '@/common/components/statisticalCards/statisticalCards.vue';
import {
  getFeedbackCounts,
  listTop20UserName,
  getUserfeedbackList,
  userfeedbackExportExcel,
  addReplyRequest,
  getFeedbackListRequest,
} from '@/common/api/data';
import { SOURCE } from '@/common/utils/constant';
import { download } from '@/common/utils/index';

export default {
  components: { statisticalCards },
  data() {
    const query = this.queryTable;
    return {
      query,
      data: [],
      topData: 0,
      columns: [
        {
          prop: 'userCode',
          label: '工号',
        },
        {
          prop: 'userName',
          label: '姓名',
        },
        {
          prop: 'feedbackContent',
          label: '反馈内容',
        },
        {
          prop: 'imageUrls',
          label: '图片',
          slot: true,
        },
        {
          prop: 'visitFromName',
          label: '访问来源',
        },
        {
          prop: 'createTime',
          label: '时间',
        },
        {
          prop: 'feedbackNum',
          label: '回复次数',
          scopedSlots: {
            default: (scope) => {
              return scope.row.responseNum > 0 ? (
                <div
                  onClick={() => {
                    this.handleInfo(scope.row.id);
                  }}
                  class="feedback-num"
                >
                  {scope.row.responseNum}
                </div>
              ) : (
                <div>0</div>
              );
            },
          },
        },
        {
          prop: 'responseStatus',
          label: '回复状态',
          align: 'center',
          scopedSlots: {
            default: (scope) => {
              const statusMap = {
                0: '未处理',
                1: '处理中',
                2: '已解决',
                3: '暂不处理',
              };
              const statusKey = scope.row.responseStatus.toString();
              const statusText = statusMap[statusKey] || '未处理';

              // 当有回复记录时才可点击查看
              return scope.row.responseNum > 0 ? (
                <div
                  onClick={() => {
                    this.handleInfo(scope.row.id);
                  }}
                  class="feedback-num"
                >
                  {statusText}
                </div>
              ) : (
                <div>{statusText}</div>
              );
            },
          },
        },
        {
          // 操作
          label: '操作',
          scopedSlots: {
            default: (scope) => {
              return (
                <el-button
                  type="text"
                  onClick={() => {
                    this.handleReply(scope.row);
                  }}
                >
                  回复
                </el-button>
              );
            },
          },
        },
      ],
      fields: [
        {
          tag: 'el-date-picker',
          name: 'time',
          label: '查询时间',
          type: 'daterange',
          valueFormat: 'yyyy-MM-dd',
        },
        {
          tag: 'el-select',
          name: 'visitFrom',
          label: '访问来源',
          type: 'select',
          childTag: 'el-option',
          options: SOURCE,
        },
        {
          tag: 'el-select',
          name: 'userName',
          label: '用户姓名',
          type: 'select',
          childTag: 'el-option',
          options: [],
          filterable: true,
          allowCreate: true,
          defaultFirstOption: true,
          remote: true,
          remoteMethod: this.remoteSearchUser,
          loading: false,
          placeholder: '请输入用户姓名或工号',
          on: {
            clear: this.clearTag,
          },
        },
        {
          tag: 'el-select',
          name: 'responseStatus',
          label: '回复状态',
          type: 'select',
          childTag: 'el-option',
          options: [
            { label: '未处理', value: '0' },
            { label: '处理中', value: '1' },
            { label: '已解决', value: '2' },
            { label: '暂不处理', value: '3' },
          ],
          placeholder: '请选择回复状态',
        },
      ],
      form: {
        time: '',
        visitFrom: '',
        userName: '',
        responseStatus: '',
      },
      total: 0,
      realtimeText: '',
      onlineUser: 456,
      userOptions: [], // 存储用户选项
      dialogVisible: false, // 回复的dialog
      replayId: '', // 回复的id
      replay: '', // 回复内容

      feedbackdialog: false,
      feedbackdata: [],
      feedbacktotal: 0,
      feedbackPage: 1,
      feedbackPageSize: 10,
      feedbackid: '',
      loading: false,
      responseStatus: '', // 回复状态
      defaultResponses: {
        1: '您反馈的问题已安排处理，感谢您提出的宝贵意见~',
        2: '您反馈的问题已解决，感谢您的耐心等待～',
        3: '已收到您的宝贵意见，项目组正在积极推进~',
      },
    };
  },

  async mounted() {
    // 初始加载一些用户数据
    await this.remoteSearchUser('');
    this.queryTopData();
  },
  methods: {
    /**
     * 处理用户搜索框重置
     */
    async reset() {
      this.remoteSearchUser('');
    },
    /**
     * 远程搜索用户
     * @param {string} query 搜索关键词
     */
    async remoteSearchUser(query) {
      // 设置对应字段的loading状态
      this.fields = this.fields.map((item) => {
        if (item.name === 'userName') {
          item.loading = true;
        }
        return item;
      });

      try {
        // 调用API获取用户列表
        const res = await listTop20UserName(query);

        if (res && res.code === 200) {
          const options = res.data.map((item) => ({
            label: item.userName,
            value: item.userCode,
          }));

          // 更新用户选项
          this.userOptions = options;

          // 更新字段的options和loading状态
          this.fields = this.fields.map((item) => {
            if (item.name === 'userName') {
              item.options = [...options];
              item.loading = false;
            }
            return item;
          });
        }
      } catch (error) {
        console.error('获取用户列表失败:', error);
        // 出错时也要关闭loading状态
        this.fields = this.fields.map((item) => {
          if (item.name === 'userName') {
            item.loading = false;
          }
          return item;
        });
      }
    },
    // blur(e){
    //   console.log('blur',e)
    // },
    // focus(v){
    //   console.log('focus',v)
    // },
    clearTag() {
      // console.log('clearTag',v);
      this.remoteSearchUser('');
    },
    setResponseStatus(status) {
      this.responseStatus = status;
      // 当选择状态时，自动填充对应的默认文本
      if (status && this.defaultResponses[status]) {
        this.replay = this.defaultResponses[status];
        console.log('设置的默认回复内容:', this.replay);
      }
    },
    /**搜索table数据 */
    async queryTable(row) {
      const params = {
        page: {
          pageNum: row.pageNum,
          pageSize: row.pageSize,
        },
        filter: {
          startTime: row.time && row.time[0] ? `${row.time[0]} 00:00:00` : null,
          endTime: row.time && row.time[1] ? `${row.time[1]} 23:59:59` : null,
          visitFrom: row.visitFrom || null,
          userCode: row.userName || null,
          responseStatus: row.responseStatus || null,
        },
      };
      const res = await getUserfeedbackList(params);
      if (res && res.code === 200) {
        this.data = res.data.dataList.map((item) => {
          // 查找对应的访问来源标签
          const visitFromObj = SOURCE.find((i) => i.value === item.visitFrom);
          return {
            ...item,
            // 添加访问来源的汉字标签
            visitFromName: visitFromObj ? visitFromObj.label : item.visitFrom,
            // 确保responseStatus是字符串类型
            responseStatus: item.responseStatus !== undefined ? item.responseStatus.toString() : '0',
          };
        });
        this.total = res.data.totalCount;
      }
    },
    /**顶部数据查询 */
    async queryTopData() {
      const res = await getFeedbackCounts();
      if (res && res.code === 200) {
        this.topData = res.data;
      }
    },
    async downloadFile() {
      const row = this.form;
      const params = {
        page: {
          pageNum: row.pageNum || 1,
          pageSize: row.pageSize || 50000,
        },
        filter: {
          startTime: row.time && row.time[0] ? `${row.time[0]} 00:00:00` : null,
          endTime: row.time && row.time[1] ? `${row.time[1]} 23:59:59` : null,
          visitFrom: row.visitFrom || null,
          userName: row.userName || null,
        },
      };
      const res = await userfeedbackExportExcel(params);
      // console.log(res.data);
      if (res && res.status === 200) {
        download(res.data, '反馈管理-详细数据列表');
      }
    },
    handleReply(row) {
      console.log(row);
      this.replayId = row.id;
      this.dialogVisible = true;
    },
    // 提交回复内容
    async handelSubClick() {
      console.log('当前状态:', this.responseStatus);
      if (!this.replay.trim()) {
        this.$message.error('请填写回复内容');
        return;
      }

      const statusToSend = this.responseStatus || '0';

      const data = {
        responseContent: this.replay.trim(),
        responseStatus: statusToSend,
      };
      console.log('发送的数据:', data);

      try {
        const res = await addReplyRequest(this.replayId, data);
        console.log('后端返回:', res);
        if (res.code !== 200) {
          this.$message.error('添加失败!');
          return;
        }

        this.$message.success('回复成功!');
        this.replay = '';
        this.replayId = '';
        this.dialogVisible = false;
        this.responseStatus = ''; // 重置状态

        // 重新查询列表数据
        const currentQuery = {
          ...this.form,
          pageNum: this.query.pageNum,
          pageSize: this.query.pageSize,
        };
        await this.queryTable(currentQuery);
      } catch (error) {
        console.error('回复失败:', error);
        this.$message.error('回复失败，请重试');
      }
    },
    handleClose() {
      this.dialogVisible = false;
    },

    async handleInfo(id) {
      this.loading = true;
      this.feedbackdialog = true;
      this.feedbackid = id;
      const data = {
        page: {
          pageNum: this.feedbackPage,
          pageSize: this.feedbackPageSize,
        },
        filter: {},
      };
      const res = await getFeedbackListRequest(id, data);
      this.loading = false;
      if (res.code === 200) {
        this.feedbackdata = res.data.dataList;
        this.feedbacktotal = res.data.totalCount;
      }
    },
    handleFeedbackCurrentChange(val) {
      this.feedbackPage = val;
      this.handleInfo(this.feedbackid);
    },
    handleFeedbackSizeChange(val) {
      this.feedbackPageSize = val;
      this.handleInfo(this.feedbackid);
    },
    handlefeedbackClose() {
      this.feedbackdialog = false;
      this.feedbackdata = [];
      this.feedbackid = '';
      this.feedbackPage = 1;
      this.feedbacktotal = 0;
    },
  },
};
</script>
<style lang="scss" scoped>
.top-container {
  gap: 10px;
}

.mb-10 {
  margin-bottom: 10px;
}

.realtime-info {
  color: #909399;
  margin-left: 10px;
  font-size: 14px;
  vertical-align: middle;
}

.image-preview-wrapper {
  position: relative;
  display: inline-block;
}

.image-count {
  position: absolute;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  font-size: 12px;
  padding: 2px 4px;
  border-radius: 2px;
}

.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: #f5f7fa;
  color: #909399;
  font-size: 20px;
}

.chartBox {
  overflow: hidden;
  margin-top: 10px;
  .chartBoxContent {
    width: calc(50% - 5px);
    float: left;
    &:nth-child(odd) {
      margin-right: 10px;
    }
  }
}
.noImage {
  height: 50px;
  line-height: 50px;
  text-align: center;
  color: #ccc;
}
.feedback-num {
  color: #409eff;
  cursor: pointer;
}
</style>
