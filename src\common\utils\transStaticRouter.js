function transStaticRouter(asyncRouterMap) {
  return asyncRouterMap.filter((route) => {
    route.isOuterChain = true;
    route.isFrame = '1';
    route.component = route.path;
    if (route.meta) {
      route.meta.menuDomain = { ...route.meta, menuDomain: '' };
    } else {
      route.meta = { menuDomain: '' };
    }
    // route={...route,isFrame:1,isOuterChain:true, meta:{...route.meta,menuDomain:''}}
    if (route.menuType == 'M') {
      route.component = 'Layout';
    }
    if (route?.children?.length) {
      route.children = transStaticRouter(route.children);
    } else {
      delete route['children'];
      delete route['redirect'];
    }
    console.log(route.clientCode);
    if (route.clientCode == process.env.VUE_APP_APPLATION_NAME) {
      console.log(process.env.VUE_APP_APPLATION_NAME);
      return true;
    } else {
      return false;
    }
  });
}
export default transStaticRouter;
